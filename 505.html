<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - Server Error | Ullens College</title>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TN01KTVJ35"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-TN01KTVJ35');
        // Remove GA4 config for GTM container ID (GT-*) — incorrect
        
        // Track 500 error
        gtag('event', 'page_view', {
            'event_category': 'Error',
            'event_label': '500 Error',
            'page_title': document.title,
            'page_location': window.location.href,
            'page_path': window.location.pathname
        });
    </script>
    <!-- Favicon -->
        <link rel="icon" type="image/svg+xml" href="/UC%20Logo.svg">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon%20-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png">
    <link rel="manifest" href="/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#1a3c6e">
    <meta name="theme-color" content="#1a3c6e">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
        }
        .logo {
            width: 150px;
            margin-bottom: 2rem;
        }
        h1 {
            color: #333;
            font-size: 3rem;
            margin: 0 0 1rem 0;
        }
        .error-message {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        .home-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #1a3c6e;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .home-button:hover {
            background-color: #0f2442;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <img src="/UC%20Logo.svg" alt="Ullens College Logo" class="logo">
        <h1>500</h1>
        <div class="error-message">
            <p>Oops! Something went wrong on our server.</p>
            <p>We're working to fix this issue. Please try again later.</p>
        </div>
        <a href="/" class="home-button">Return to Homepage</a>
    </div>
</body>
</html>
