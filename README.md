## UC Web

A minimal, static website scaffold with sensible defaults for favicons, SEO, and custom error pages.

### Features
- **Static HTML**: `index.html`, `404.html`, `505.html`
- **Favicons**: ready-to-use assets and link snippet in `favicon/`
- **SEO**: `Sitemap.xml`, `robots.txt` (see note below)
- **Branding**: `UC Logo.svg`

### Project Structure
```
UC_web/
├─ index.html
├─ 404.html
├─ 505.html
├─ favicon/
│  ├─ android-chrome-192x192.png
│  ├─ android-chrome-512x512.png
│  ├─ apple-touch-icon.png
│  ├─ favicon-16x16.png
│  ├─ favicon -32x32.png
│  ├─ favicon-links.html    # Paste these <link> tags into <head>
│  └─ site.webmanifest
├─ robots.txt.txt            # Rename to robots.txt before/at deploy
├─ Sitemap.xml
└─ UC Logo.svg
```

### Quick Start (Local Preview)
- macOS (built-in):
  ```bash
  cd /Users/<USER>/Downloads/UC_web
  python3 -m http.server 8080
  # Open http://localhost:8080
  ```
- Node.js (if installed):
  ```bash
  npx serve . -l 8080
  # or: npx http-server . -p 8080 -c-1 --silent
  ```

### Favicon Setup
- Open `favicon/favicon-links.html` and copy its `<link>` tags into the `<head>` of `index.html` (and any other pages) to ensure all platforms pick up the icons and `site.webmanifest`.

### SEO
- **Sitemap**: Update domain URLs in `Sitemap.xml` before deploying.
- **Robots**: The file is currently `robots.txt.txt`. Rename to `robots.txt` in production so crawlers can discover it.

### Error Pages
- `404.html` is widely supported (GitHub Pages, Netlify, Vercel).
- `505.html` may need host-specific configuration (e.g., custom 500 handler); some hosts ignore custom 5xx pages.

### Deploy
- **GitHub Pages**: Serve from the repository root or `docs/`. Ensure `404.html` is at the output root.
- **Netlify/Vercel**: Set the output directory to the project root; both auto-detect `404.html`.
- **S3/CloudFront**: Configure index and error document to `index.html` and `404.html` respectively.

### Contributing
- Keep the design minimal and consistent.
- Favor semantic HTML and accessible markup.

### License
No license specified. If you need one, add a `LICENSE` file.


