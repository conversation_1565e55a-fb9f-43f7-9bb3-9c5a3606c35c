<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Join the first-ever class of cybersecurity experts at Ullens College. World-class B.Tech in Cybersecurity through partnership with Kathmandu University.">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TN01KTVJ35"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-TN01KTVJ35');
        // Remove GA4 config for GTM container ID (GT-*) — incorrect

        // Scroll Depth Tracking
        let scrollDepths = [25, 50, 75, 90];
        let reachedDepths = new Set();

        function calculateScrollDepth() {
            const winHeight = window.innerHeight;
            const docHeight = document.documentElement.scrollHeight - winHeight;
            const scrollTop = window.scrollY;
            const scrollPercent = (scrollTop / docHeight) * 100;

            scrollDepths.forEach(depth => {
                if (scrollPercent >= depth && !reachedDepths.has(depth)) {
                    reachedDepths.add(depth);
                    gtag('event', 'scroll_depth', {
                        'depth': depth,
                        'page_title': document.title,
                        'page_location': window.location.href
                    });
                }
            });
        }

        // Track Apply Now Button Clicks
        function trackApplyNowClick(element) {
            gtag('event', 'apply_now_click', {
                'event_category': 'Engagement',
                'event_label': 'Apply Now Button Click',
                'page_title': document.title,
                'page_location': window.location.href
            });
        }

        // Add event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll tracking
            window.addEventListener('scroll', _.throttle(calculateScrollDepth, 1000));

            // Apply Now button tracking
            document.querySelectorAll('a[href*="apply"], button[data-action="apply"]').forEach(button => {
                button.addEventListener('click', function(e) {
                    trackApplyNowClick(this);
                });
            });
        });
    </script>
    <title>Ullens College | B.Tech in Cybersecurity</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/UC%20Logo.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon%20-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png">
    <link rel="manifest" href="/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#1a3c6e">
    <meta name="theme-color" content="#1a3c6e">
    
    <!-- PWA Manifest -->
    
    <!-- Performance Optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://unpkg.com">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="UC%20Logo.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Fira+Code:wght@400;500;600&display=swap" as="style">
    
    <!-- External Stylesheet (Optimized) -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Fira+Code:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Critical CSS inline for above-the-fold content -->
    <style>
        :root{--bg-color:#FFFFFF;--text-color:#0F172A;--primary-color:#c82f48;}
        body{margin:0;font-family:Inter,-apple-system,sans-serif;background:#FAFAF7;}
        .hero{min-height:100vh;display:flex;align-items:center;justify-content:center;}
        .progress-bar{position:fixed;top:0;left:0;width:0%;height:4px;background:linear-gradient(90deg,var(--primary-color),#3B82F6);z-index:1000;}
    </style>
    
    <!-- Scripts loaded asynchronously with performance hints -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js" defer></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js" defer></script>
    
    <!-- Service Worker for caching (optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js').catch(() => {});
            });
        }
    </script>
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Sticky Header -->
    <header class="sticky-header" id="stickyHeader">
        <div class="container">
            <img src="UC%20Logo.svg" alt="Ullens College" class="logo">
            <nav>
                <ul class="nav-links-desktop">
                    <li><a href="#vision">Vision</a></li>
                    <li><a href="#values">Values</a></li>
                    <li><a href="#briefing">Program</a></li>
                    <li><a href="#advantage">Advantage</a></li>
                    <li><a href="#future">Future</a></li>
                    <li><a href="#team">Team</a></li>
                    <li><a href="#logistics">FAQs</a></li>
                </ul>
            </nav>
            <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button" target="_blank" rel="noopener noreferrer" style="padding: var(--space-2) var(--space-4); font-size: var(--text-sm); min-height: auto;">
                <span>Apply Now</span>
            </a>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <img src="UC%20Logo.svg" alt="Ullens College" style="height: 40px;">
        <button class="nav-toggle" id="navToggle">☰</button>
    </nav>

    <!-- Navigation Menu -->
    <div class="nav-overlay" id="navOverlay"></div>
    <div class="nav-menu" id="navMenu">
        <button class="nav-toggle" id="navClose" style="margin-left: auto; display: block;">✕</button>
        <ul class="nav-links">
            <li><a href="#vision">Vision</a></li>
            <li><a href="#values">Values</a></li>
            <li><a href="#briefing">Program</a></li>
            <li><a href="#advantage">Advantage</a></li>
            <li><a href="#future">Future</a></li>
            <li><a href="#team">Team</a></li>
            <li><a href="#logistics">FAQs</a></li>
            <li><a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" target="_blank" rel="noopener noreferrer" class="cta-button" style="margin-top: 1rem; text-align: center;"><span>Apply Now</span></a></li>
        </ul>
    </div>

    <!-- Section 1: Hero -->
    <main id="main-content">
    <section class="hero" id="home" role="region" aria-label="Hero">
        <div class="parallax-bg"></div>
        <div class="network-bg" id="networkBg"></div>
        <div class="hero-content">
            <div class="eyebrow-badge fade-in" aria-label="Admissions 2025">
                <span class="eyebrow-dot" aria-hidden="true"></span>
                <span>Admissions 2025 • Limited seats</span>
            </div>
            <img src="UC%20Logo.svg" alt="Ullens College Logo" class="hero-logo fade-in">
            <h1 class="fade-in">B.Tech in Cybersecurity</h1>
            <h3 class="hero-tagline fade-in">Where innovation meets integrity</h3>
            <h3 class="hero-subtitle fade-in">Affiliated with Kathmandu University</h3>
            <p class="hero-description fade-in">A world‑class 4‑year program blending theory and hands‑on labs, guided by industry mentors—right here in Khumaltar.</p>
            <div class="hero-cta">
                <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button ripple fade-in" target="_blank" rel="noopener noreferrer">
                    <span>APPLY NOW</span>
                </a>
                <p class="fade-in" style="color: var(--text-muted); font-size: var(--text-sm); margin-top: var(--space-3);">Takes ~2 minutes on Google Forms</p>
            </div>
        </div>

    </section>

    <div class="container">
        <!-- Vision Section -->
        <section id="vision" class="vision-section fade-in">
            <div class="vision-content">
                <h2>Vision</h2>
                <p class="vision-statement">
                    Ullens College aims to <span class="vision-emphasis">set standards of excellence</span> by nurturing <span class="vision-emphasis">technologically advanced and ethically grounded graduates</span> who embrace <span class="vision-emphasis">interdisciplinary learning</span>, <span class="vision-emphasis">think deeply</span>, <span class="vision-emphasis">solve problems creatively</span>, and act with a <span class="vision-emphasis">global mindset</span> in a rapidly changing world.
                </p>
            </div>
        </section>

        <!-- Values Section -->
        <section id="values" class="fade-in">
            <div class="values-section">
                <div class="container">
                    <div class="values-intro">
                        <h2>Our Core Values</h2>
                        <p class="values-subtitle">
                            The principles that guide our mission to create ethical, innovative, and globally-minded cybersecurity leaders.
                        </p>
                    </div>
                    
                    <div class="values-grid">
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="target"></i>
                            </div>
                            <div class="value-content">
                                <h3>Excellence and Deep Inquiry</h3>
                                <p>We pursue the <span class="value-emphasis">highest standards</span> in teaching, research, and personal growth. We believe that true excellence is rooted in <span class="value-emphasis">curiosity, rigorous thinking</span>, and a relentless drive to <span class="value-emphasis">understand the world more deeply</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="palette"></i>
                            </div>
                            <div class="value-content">
                                <h3>Creativity and Innovation</h3>
                                <p>We thrive at the <span class="value-emphasis">intersections</span>—where disciplines meet, ideas collide, and new solutions emerge. We foster <span class="value-emphasis">creative problem-solving</span> and empower our community to <span class="value-emphasis">cross boundaries and innovate</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="scale"></i>
                            </div>
                            <div class="value-content">
                                <h3>Integrity and Ethics</h3>
                                <p>We hold ourselves to the highest standards of <span class="value-emphasis">honesty, transparency, and ethical conduct</span>. Technological advancement must be guided by a <span class="value-emphasis">strong moral compass</span> and <span class="value-emphasis">serve the greater good</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="globe"></i>
                            </div>
                            <div class="value-content">
                                <h3>Global Mindset</h3>
                                <p>We prepare graduates to <span class="value-emphasis">thrive in a rapidly changing, interconnected world</span>. We cultivate <span class="value-emphasis">cross-cultural understanding</span>, <span class="value-emphasis">global awareness</span>, and adaptability to new environments and ideas.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="users"></i>
                            </div>
                            <div class="value-content">
                                <h3>Community and Belonging</h3>
                                <p>We build a <span class="value-emphasis">welcoming, inclusive, and supportive community</span> where everyone can flourish. We <span class="value-emphasis">value diversity in all its forms</span> and believe <span class="value-emphasis">every voice enriches our collective journey</span>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: The Program -->
        <section id="briefing" class="program-section fade-in">
            <div class="program-content">
                <h2>The Program: B.Tech. in Cybersecurity</h2>
                <p class="program-description">
                    Think of this as your <span class="program-emphasis">four-year launchpad</span> to becoming an <span class="program-emphasis">expert digital defender</span>. It's a <span class="program-emphasis">full-time, hands-on program</span>, affiliated with Kathmandu University, where you'll learn to <span class="program-emphasis">design, defend, and dominate</span> in the world of digital security. We don't just teach you to use the tools; <span class="program-highlight">we teach you to build them.</span>
                </p>

                <div style="text-align: center; margin-top: var(--space-8);">
                    <a href="https://www.canva.com/design/DAGvc8alXlc/6s6xtwmz0lsYmylgTFneEA/view?utm_content=DAGvc8alXlc&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=hc56c1e4eaa"
                       target="_blank"
                       rel="noopener noreferrer"
                        class="cta-button"
                        style="background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple)); display: inline-flex; align-items: center; gap: 0.5rem; font-size: var(--text-base);">
                        <i data-lucide="book-open" style="width: 20px; height: 20px;"></i>
                        <span>View Curriculum</span>
                    </a>
                </div>
            </div>
        </section>

        <!-- Section 7: Team -->
        <section id="team" class="fade-in">
            <div class="team-section">
                <div class="container">
                    <div class="team-intro-container">
                        <h2>Meet Our Expert Team</h2>
                        <p class="team-intro">
                            Our program is powered by a <span class="team-emphasis">world-class faculty</span> combining <span class="team-emphasis">academic excellence</span> with <span class="team-emphasis">industry expertise</span>. From seasoned researchers to cybersecurity practitioners, our team brings together <span class="team-emphasis">cutting-edge knowledge</span>, <span class="team-emphasis">real-world experience</span>, and <span class="team-emphasis">innovative teaching methods</span> to shape the next generation of digital defenders.
                        </p>
                    </div>
                                         <div class="team-grid">
                         <div class="team-card fade-in">
                             <div class="team-avatar">SA</div>
                             <h3>Shishir Adhikari, PhD</h3>
                             <p class="team-role">Academic Director</p>
                             <a href="https://www.linkedin.com/in/arshishir/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Shishir Adhikari LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">TG</div>
                             <h3>Topraj Gurung, PhD</h3>
                             <p class="team-role">Industry Faculty</p>
                             <a href="https://www.linkedin.com/in/topraj/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Topraj Gurung LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">SL</div>
                             <h3>Saroj Lamichhane</h3>
                             <p class="team-role">Industry Faculty</p>
                             <a href="https://www.linkedin.com/in/sarojl/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Saroj Lamichhane LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">MS</div>
                             <h3>Manish Sharma</h3>
                             <p class="team-role">Industry Faculty</p>
                             <a href="https://www.linkedin.com/in/manishksharma01/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Manish Sharma LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">MA</div>
                             <h3>Monil Adhikari</h3>
                             <p class="team-role">Founding Faculty</p>
                             <a href="https://www.linkedin.com/in/moniladhikari/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Monil Adhikari LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">HK</div>
                             <h3>Himalaya Kakshapati</h3>
                             <p class="team-role">Founding Faculty</p>
                             <a href="https://www.linkedin.com/in/himalaya-kakshapati/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Himalaya Kakshapati LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">SD</div>
                             <h3>Saroj Dhakal</h3>
                             <p class="team-role">Director of Technology</p>
                             <a href="https://www.linkedin.com/in/sarojdhakal" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Saroj Dhakal LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                         <div class="team-card fade-in">
                             <div class="team-avatar">SB</div>
                             <h3>Sulav Bhatta</h3>
                             <p class="team-role">Head of Career Counseling</p>
                             <a href="https://www.linkedin.com/in/sulav-raj-bhatta-a01718186/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Sulav Bhatta LinkedIn Profile">
                                 <i data-lucide="linkedin"></i>
                                 Connect on LinkedIn
                             </a>
                         </div>
                     </div>
                </div>
            </div>
        </section>

        <!-- Section 3: Why We're Different -->
        <section id="advantage" class="fade-in">
            <div class="advantage-section">
                <div class="container">
                    <div class="advantage-intro">
                        <h2>The Ullens Advantage</h2>
                        <p style="text-align: center; max-width: 900px; margin: 0 auto; font-size: var(--text-lg); line-height: var(--leading-relaxed); color: var(--text-secondary);">
                            We built this program differently from the ground up. Our revolutionary approach combines engineering excellence, security mastery, future-ready AI integration, and leadership development into one transformative educational experience.
                        </p>
                    </div>
                    <div class="advantage-grid">
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="building-2"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Architect's Blueprint</h3>
                                <p>The <span class="advantage-emphasis">backbone of our course is cutting-edge computer engineering</span>. You first learn how to build complex digital systems from scratch—from hardware fundamentals to software architecture. You can't protect a fortress until you understand every stone in its foundation.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="shield-check"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Fortress Walls</h3>
                                <p>We then <span class="advantage-emphasis">fortify that engineering core with layers of advanced cybersecurity</span>. You'll master ethical hacking, digital forensics, threat intelligence, and incident response to defend the systems you now understand at the deepest level.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="brain-circuit"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Futurist's Toolkit</h3>
                                <p>The <span class="advantage-emphasis">future is AI-driven</span>, and so are its threats. We've <span class="advantage-emphasis">integrated AI courses</span> throughout the curriculum—from machine learning security to AI-powered threat detection—ensuring you're prepared for tomorrow's cyber landscape.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="lightbulb"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Leader's Mindset</h3>
                                <p>Our <span class="advantage-emphasis">guiding philosophy is inspired by the liberal arts</span>—we <span class="advantage-emphasis">focus on how to think, not just what to think</span>. Through <span class="advantage-emphasis">project-based learning, critical thinking, communication, and problem-solving skills</span>, you'll become <span class="advantage-emphasis">a leader, not just a technician</span>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Your Future - Careers & Global Opportunities -->
        <section id="future" class="future-section fade-in">
            <div class="future-intro-container">
                <h2>Your Future Awaits</h2>
                <p class="future-intro">
                    Step into a world of <span class="future-emphasis">limitless possibilities</span> where your cybersecurity expertise opens doors to <span class="future-emphasis">high-impact careers</span> and <span class="future-emphasis">global opportunities</span>. Your journey doesn't end with graduation—it's just the beginning.
                </p>
            </div>
            <div class="future-grid">
                <div class="future-card fade-in">
                    <h3>Career Prospects</h3>
                    <p><strong>What kind of job can I get?</strong></p>
                    <p>Cybersecurity is one of the <span class="future-emphasis">fastest-growing and highest-paid fields globally</span>. Your future career battlegrounds could include:</p>
                    <ul>
                        <li>Penetration Tester (Ethical Hacker)</li>
                        <li>Digital Forensics Investigator</li>
                        <li>Security Architect</li>
                        <li>Chief Information Security Officer (CISO)</li>
                        <li>Security Consultant</li>
                        <li>Software Engineer</li>
                    </ul>
                </div>
                <div class="future-card fade-in">
                    <h3>Global Master's Pathways</h3>
                    <p><strong>Can I use this degree to study abroad?</strong></p>
                    <p><span class="future-emphasis">Absolutely!</span> This degree is your <span class="future-emphasis">ticket to advanced studies worldwide</span>. Plus, our sister organization, <a href="https://ullensleap.org/" target="_blank" rel="noopener noreferrer">LEAP</a>, provides expert counseling to help you target and apply to the <span class="future-emphasis">best institutions</span> in the US, UK, Australia, and beyond.</p>
                </div>
            </div>
        </section>

        <!-- Section 6: The Logistics -->
        <section id="logistics" class="fade-in">
            <div class="faq-header">
                <h2>Frequently Asked Questions</h2>
                <p class="faq-subtitle">Everything you need to know about joining our pioneering cybersecurity program</p>
            </div>
            <div class="accordion">
                <div class="accordion-item">
                    <button class="accordion-button">Who can apply? Am I eligible?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Good news:</strong> If you're passionate about tech and have completed <strong class="faq-emphasis">+2, A-Levels, IBDP,or equivalent</strong> with <strong class="faq-emphasis">Mathematics and Computer Science</strong>, you're eligible to apply.</p>
                            
                            <div class="faq-details">
                                <h4>What we're looking for:</h4>
                                <ul>
                                    <li><strong>Curious minds</strong> from Science and Management</li>
                                    <li><strong>Problem-solving enthusiasm</strong> (not just grades)</li>
                                    <li><strong>Genuine interest</strong> in technology and cybersecurity</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <button class="accordion-button">What's the entrance exam like?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Simple format:</strong> A <strong class="faq-emphasis">2-hour Paper-Based Test (PBT)</strong> covering three core areas:</p>
                            
                            <div class="faq-details">
                                <div class="exam-breakdown">
                                    <div class="exam-subject">
                                        <h4>English</h4>
                                        <p>Comprehension, Grammar, Vocabulary, etc.</p>
                                    </div>
                                    <div class="exam-subject">
                                        <h4>Mathematics</h4>
                                        <p>Logical reasoning, Calculus, Linear Algebra,etc.</p>
                                    </div>
                                    <div class="exam-subject">
                                        <h4>Computer Science</h4>
                                        <p>Algorithmic thinking, Basic programming, etc.</p>
                                    </div>
                                </div>
                                <p><strong>Note:</strong> We test <em>aptitude and thinking</em>, not memorization.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <button class="accordion-button">What are the important dates and test center?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Mark your calendar:</strong> Here's the complete timeline for <strong class="faq-emphasis">2025 admissions</strong>:</p>
                            
                            <div class="faq-details">
                                <div class="timeline-grid">
                                    <div class="timeline-item">
                                        <div class="timeline-date">August 6, 2025</div>
                                        <div class="timeline-event">Registration Opens</div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-date">August 26, 2025</div>
                                        <div class="timeline-event">Registration Closes</div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-date">August 29, 2025</div>
                                        <div class="timeline-event">Entrance Exam (PBT)</div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-date">September 1, 2025</div>
                                        <div class="timeline-event">Results Announced</div>
                                    </div>
                                </div>
                                
                                <div class="test-center-info">
                                    <h4>📍 Test Center Location:</h4>
                                    <p><strong>Kathmandu University School of Management</strong><br>
                                    Balkumari, Lalitpur</p>
                                </div>
                                
                                <p><strong>Pro tip:</strong> Don't wait until the last minute—register early to secure your spot!</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <button class="accordion-button">What's the investment? (Program Cost)</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Transparent approach:</strong> For the most current fee structure and <strong class="faq-emphasis">scholarship opportunities</strong>, contact our admissions team directly.</p>
                            
                            <div class="faq-details">
                                <div class="contact-promise">
                                    <p><strong>Our promise:</strong> <em>Zero pressure, complete transparency.</em> We'll provide all details about costs, payment plans, and financial aid options.</p>
                                </div>
                                
                                <div class="contact-info">
                                    <p><strong>Quick contact:</strong></p>
                                    <ul>
                                        <li>📧 <a href="mailto:<EMAIL>"><EMAIL></a></li>
                                        <li>📞 <a href="tel:+977-1-5411137">977-1-5411137</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <button class="accordion-button">When does the program start and how long is it?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Timeline:</strong> This is a <strong class="faq-emphasis">4-year full-time B.Tech program</strong> starting in <strong class="faq-emphasis">2025</strong>.</p>
                            
                            <div class="faq-details">
                                <div class="program-structure">
                                    <h4>Program Structure:</h4>
                                    <ul>
                                        <li><strong>Years 1-2:</strong> Engineering fundamentals + basic cybersecurity</li>
                                        <li><strong>Years 3-4:</strong> Advanced cybersecurity + AI integration + capstone projects</li>
                                    </ul>
                                </div>
                                <p><strong>Location:</strong> Beautiful Khumaltar campus with state-of-the-art labs</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <button class="accordion-button">What makes this program different from other computer science degrees?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Unique approach:</strong> While most programs teach you to <em>use</em> technology, we teach you to <strong class="faq-emphasis">build and defend</strong> it.</p>
                            
                            <div class="faq-details">
                                <div class="differentiators">
                                    <h4>What sets us apart:</h4>
                                    <ul>
                                        <li><strong>Engineering + Security:</strong> Deep technical foundation plus specialized cybersecurity expertise</li>
                                        <li><strong>AI Integration:</strong> Future-ready curriculum with AI/ML security focus</li>
                                        <li><strong>Industry Partnership:</strong> Real-world projects with actual companies</li>
                                        <li><strong>Small Cohorts:</strong> Personalized attention and mentorship</li>
                                        <li><strong>Global Perspective:</strong> International study opportunities and partnerships</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <button class="accordion-button">Can I work while studying or is it full-time only?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p><strong class="faq-highlight">Full commitment required:</strong> This is a <strong class="faq-emphasis">full-time intensive program</strong> designed for maximum learning impact.</p>
                            
                            <div class="faq-details">
                                <div class="time-commitment">
                                    <h4>Why full-time?</h4>
                                    <ul>
                                        <li><strong>Hands-on labs:</strong> Extensive practical sessions require campus presence</li>
                                        <li><strong>Team projects:</strong> Collaborative work with classmates and industry partners</li>
                                        <li><strong>Research opportunities:</strong> Faculty-guided research projects</li>
                                        <li><strong>Internships:</strong> Structured industry placements during breaks</li>
                                    </ul>
                                </div>
                                <p><strong>Investment mindset:</strong> Think of these 4 years as building the foundation for a lifetime career in a high-growth field.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 7: Scholarship -->
        <section class="scholarship fade-in">
            <div class="scholarship-content">
                <i data-lucide="heart-handshake" class="scholarship-icon"></i>
                
                <h2>Talent is Universal. Opportunity Should Be Too.</h2>
                
                <p class="scholarship-subtitle">
                    Breaking barriers to world-class cybersecurity education
                </p>
                
                <p class="scholarship-description">
                    We believe your potential—not your financial situation—should define your future. Ullens College is committed to ensuring that the brightest minds from all backgrounds can access world-class cybersecurity education and join our mission to safeguard the digital world.
                </p>

                <div class="scholarship-highlight">
                    <div class="highlight-title">Need-Blind Scholarship Program</div>
                    <p class="highlight-text">
                        Our admissions process evaluates talent, passion, and potential—never your ability to pay. Financial need will never be a barrier to exceptional education.
                    </p>
                </div>
                
                <div class="scholarship-highlight" style="background-color: rgba(200, 47, 72, 0.15); margin-top: 12px;">
                    <div class="highlight-title">Limited Enrollment Opportunity</div>
                    <p class="highlight-text">
                        The B.Tech in Cybersecurity program has limited seats to ensure personalized attention and optimal learning outcomes. Early applications are strongly encouraged.
                    </p>
                </div>

                <div class="scholarship-features">
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="graduation-cap" class="feature-icon"></i>
                        <h3>Merit-Based Selection</h3>
                        <p>Scholarships awarded purely on academic excellence, potential, and passion for cybersecurity—regardless of financial background.</p>
                    </div>
                    
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="users" class="feature-icon"></i>
                        <h3>Diverse Community</h3>
                        <p>Building a vibrant learning environment where students from all socioeconomic backgrounds learn and grow together.</p>
                    </div>
                    
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="globe" class="feature-icon"></i>
                        <h3>Global Impact</h3>
                        <p>Empowering talented individuals to become cybersecurity leaders who will protect our digital future, regardless of their origin.</p>
                    </div>
                </div>

                <div class="scholarship-cta">
                    <a href="#footer" class="cta-button">
                        <span>Contact Us</span>
                    </a>
                </div>
            </div>
        </section>


    </div>

    <footer id="footer">
        <div class="footer-content">
            <div class="footer-main">
                <img src="UC%20Logo.svg" alt="Ullens College" class="footer-logo">
                <h3>Get in Touch</h3>
                <p class="footer-description">
                    Connect with us to learn more about our innovative cybersecurity program at Ullens College.
                </p>
                
                <div class="footer-contact">
                    <div class="contact-item">
                        <i data-lucide="map-pin" class="contact-icon"></i>
                        <p>Khumaltar, Lalitpur-15, Nepal</p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="phone" class="contact-icon"></i>
                        <p><a href="tel:+977-1-5411137">977-1-5411137</a></p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="mail" class="contact-icon"></i>
                        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="clock" class="contact-icon"></i>
                        <p>Mon - Fri: 9:00 AM - 5:00 PM</p>
                    </div>
                </div>
                
                <div class="footer-social">
                    <a href="https://www.facebook.com/profile.php?id=61577290536656" class="social-link" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                        <i data-lucide="facebook"></i>
                    </a>
                    <a href="https://www.linkedin.com/company/ullens-college" class="social-link" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                        <i data-lucide="linkedin"></i>
                    </a>
                    <a href="https://ullenscollege.vercel.app/" class="social-link" target="_blank" rel="noopener noreferrer" style="background: linear-gradient(135deg, #3B82F6, #10B981);" aria-label="Preview our future site">
                        <i data-lucide="sparkles"></i>
                    </a>
                </div>
                
                <div style="margin-top: 15px; font-size: var(--text-sm); background: linear-gradient(90deg, #3B82F6, #10B981); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; display: inline-block; border-bottom: 1px dashed #CBD5E1; padding-bottom: 2px; cursor: pointer;" class="easter-egg">
                    <i data-lucide="wand-sparkles" style="width: 14px; height: 14px; vertical-align: middle; margin-right: 4px; stroke: #3B82F6;"></i>
                    <span>Psst! Want to see something magical?</span>
                </div>
                
                <script>
                    document.querySelector('.easter-egg').addEventListener('click', function() {
                        window.open('https://ullenscollege.vercel.app/', '_blank');
                    });
                </script>
            </div>
            
            <div class="footer-cta">
                <i data-lucide="rocket" style="width: 40px; height: 40px; color: var(--primary-color); margin-bottom: var(--space-3);"></i>
                <h4>Ready to Launch Your Future?</h4>
                <p>Join the pioneering class of cybersecurity experts at Ullens College.</p>
                <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button ripple" target="_blank" rel="noopener noreferrer" style="margin: 0;">
                    <span>APPLY NOW</span>
                </a>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2025 Ullens College. All rights reserved. | Designed with love for UEF <span class="designer-credit" style="cursor: help; position: relative; border-bottom: 1px dotted rgba(200, 47, 72, 0.4);">♥</span></p>
            
            <script>
                document.querySelector('.designer-credit').addEventListener('click', function() {
                    // Create toast element
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '30px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.background = 'rgba(255, 255, 255, 0.95)';
                    toast.style.backdropFilter = 'blur(10px)';
                    toast.style.padding = '10px 20px';
                    toast.style.borderRadius = '8px';
                    toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                    toast.style.fontWeight = '500';
                    toast.style.fontSize = '14px';
                    toast.style.color = 'var(--primary-darker)';
                    toast.style.border = '1px solid rgba(200, 47, 72, 0.2)';
                    toast.style.zIndex = '1000';
                    toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(-50%) translateY(20px)';
                    toast.innerHTML = 'Crafted with ♥ by <strong>Shishir</strong>';
                    
                    // Add to DOM
                    document.body.appendChild(toast);
                    
                    // Trigger animation
                    setTimeout(() => {
                        toast.style.opacity = '1';
                        toast.style.transform = 'translateX(-50%) translateY(0)';
                    }, 10);
                    
                    // Remove after delay
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(-50%) translateY(20px)';
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 300);
                    }, 3000);
                });
            </script>
        </div>
    </footer>
    </main>

    <script>
        // Modern App Architecture
        class UllensCollegeApp {
            constructor() {
                this.components = new Map();
                this.initializeApp();
            }

            async initializeApp() {
                // Performance optimization
                this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
                
                // Initialize all components
                this.components.set('accordion', new AccordionComponent());
                this.components.set('navigation', new NavigationComponent());
                this.components.set('scrollEffects', new ScrollEffectsComponent());
                this.components.set('animations', new AnimationsComponent());
                
                // Run an initial UI update so header/progress reflect current scroll position
                this.components.get('scrollEffects').update();

                // Setup global event listeners
                this.setupEventListeners();
                this.setupIntersectionObserver();
                
                // Register service worker for PWA
                this.registerServiceWorker();
                
                console.log('✅ Ullens College App initialized');
            }

            setupEventListeners() {
                // Modern event delegation
                document.addEventListener('click', this.handleGlobalClick.bind(this));
                window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));
                window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));

                // Handle orientation changes (mobile viewport switching)
                window.addEventListener('orientationchange', this.debounce(() => {
                    console.log('📱 Orientation changed - reinitializing observers');
                    setTimeout(() => {
                        this.setupIntersectionObserver();
                        this.checkVisibleElements();
                    }, 300); // Longer delay for orientation changes
                }, 100));

                // Modern keyboard navigation
                document.addEventListener('keydown', this.handleKeyboard.bind(this));
            }

            handleGlobalClick(event) {
                const target = event.target.closest('[data-action]');
                if (!target) return;

                const action = target.dataset.action;
                const component = target.dataset.component;

                if (component && this.components.has(component)) {
                    this.components.get(component).handleAction(action, target, event);
                }
            }

            handleScroll() {
                this.components.get('scrollEffects').update();
            }

            handleResize() {
                // Re-initialize intersection observer on significant viewport changes
                // This fixes mobile viewport switching issues
                if (this.intersectionObserver) {
                    console.log('🔄 Viewport changed - reinitializing intersection observer');
                    setTimeout(() => {
                        this.setupIntersectionObserver();
                        // Force check visibility of elements currently in viewport
                        this.checkVisibleElements();
                    }, 150); // Small delay to ensure viewport has settled
                }

                this.components.forEach(component => {
                    if (component.handleResize) component.handleResize();
                });
            }

            handleKeyboard(event) {
                // ESC key closes modals/menus
                if (event.key === 'Escape') {
                    this.components.get('navigation').close();
                }

                // Accordion keyboard support: Up/Down/Home/End
                const focused = document.activeElement;
                if (focused && focused.classList && focused.classList.contains('accordion-button')) {
                    const buttons = Array.from(document.querySelectorAll('.accordion-button'));
                    const idx = buttons.indexOf(focused);
                    if (event.key === 'ArrowDown') {
                        event.preventDefault();
                        buttons[Math.min(idx + 1, buttons.length - 1)]?.focus();
                    } else if (event.key === 'ArrowUp') {
                        event.preventDefault();
                        buttons[Math.max(idx - 1, 0)]?.focus();
                    } else if (event.key === 'Home') {
                        event.preventDefault();
                        buttons[0]?.focus();
                    } else if (event.key === 'End') {
                        event.preventDefault();
                        buttons[buttons.length - 1]?.focus();
                    }
                }
            }

            setupIntersectionObserver() {
                // Disconnect existing observer if it exists
                if (this.intersectionObserver) {
                    this.intersectionObserver.disconnect();
                }

                const observerOptions = {
                    threshold: [0.05, 0.1, 0.25], // Multiple thresholds for better mobile detection
                    rootMargin: '0px 0px -5% 0px' // Less aggressive margin for mobile compatibility
                };

                this.intersectionObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                            // Keep observing for viewport changes - don't unobserve
                        } else {
                            // Optional: Re-hide elements that go completely out of view
                            // Only on very small intersections to avoid flickering
                            if (entry.intersectionRatio < 0.05) {
                                // entry.target.classList.remove('visible'); // Uncomment if you want re-hiding
                            }
                        }
                    });
                }, observerOptions);

                // Observe all fade-in elements
                document.querySelectorAll('.fade-in').forEach(el => {
                    this.intersectionObserver.observe(el);
                });

                console.log('✅ Intersection Observer initialized with mobile-friendly settings');
            }

            checkVisibleElements() {
                // Force-check elements that should be visible in current viewport
                // This helps with mobile viewport switching issues
                document.querySelectorAll('.fade-in').forEach(el => {
                    const rect = el.getBoundingClientRect();
                    const windowHeight = window.innerHeight;

                    // If element is in viewport (with some tolerance)
                    if (rect.top < windowHeight * 0.9 && rect.bottom > windowHeight * 0.1) {
                        el.classList.add('visible');
                    }
                });
            }

            async registerServiceWorker() {
                if ('serviceWorker' in navigator) {
                    try {
                        // Inline service worker for minimal setup
                        const swCode = `
                            self.addEventListener('install', () => self.skipWaiting());
                            self.addEventListener('activate', () => self.clients.claim());
                            self.addEventListener('fetch', event => {
                                if (event.request.url.includes('.css') || event.request.url.includes('.js')) {
                                    event.respondWith(
                                        caches.open('ullens-v1').then(cache => 
                                            cache.match(event.request) || fetch(event.request)
                                        )
                                    );
                                }
                            });
                        `;
                        
                        const blob = new Blob([swCode], { type: 'application/javascript' });
                        const registration = await navigator.serviceWorker.register(URL.createObjectURL(blob));
                        console.log('✅ Service Worker registered');
                    } catch (error) {
                        console.log('ℹ️ Service Worker registration failed:', error);
                    }
                }
            }

            // Utility functions
            throttle(func, limit) {
                let inThrottle;
                return function() {
                    if (!inThrottle) {
                        func.apply(this, arguments);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }

            debounce(func, wait) {
                let timeout;
                return function(...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(this, args), wait);
                };
            }
        }

        // Accordion Component
        class AccordionComponent {
            constructor() {
                this.items = document.querySelectorAll('.accordion-item');
                this.init();
            }

            init() {
                this.items.forEach((item, index) => {
                    const button = item.querySelector('.accordion-button');
                    const content = item.querySelector('.accordion-content');
                    const contentId = `accordion-content-${index+1}`;
                    button.dataset.action = 'toggle';
                    button.dataset.component = 'accordion';
                    button.setAttribute('aria-expanded', 'false');
                    button.setAttribute('aria-controls', contentId);
                    button.setAttribute('role', 'button');
                    content.id = contentId;
                    content.setAttribute('role', 'region');
                    content.setAttribute('aria-labelledby', contentId);
                });
            }

            handleAction(action, target, event) {
                if (action === 'toggle') {
                    this.toggle(target.closest('.accordion-item'));
                }
            }

            toggle(item) {
                const content = item.querySelector('.accordion-content');
                const isActive = item.classList.contains('active');

                // Close other items
                this.items.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        this.close(otherItem);
                    }
                });

                // Toggle current item
                if (isActive) {
                    this.close(item);
                } else {
                    this.open(item);
                }
            }

            open(item) {
                const content = item.querySelector('.accordion-content');
                const button = item.querySelector('.accordion-button');
                item.classList.add('active');
                content.style.maxHeight = content.scrollHeight + 'px';
                button.setAttribute('aria-expanded', 'true');
            }

            close(item) {
                const content = item.querySelector('.accordion-content');
                const button = item.querySelector('.accordion-button');
                item.classList.remove('active');
                content.style.maxHeight = '0';
                button.setAttribute('aria-expanded', 'false');
            }
        }

        // Navigation Component
        class NavigationComponent {
            constructor() {
                this.navToggle = document.getElementById('navToggle');
                this.navClose = document.getElementById('navClose');
                this.navMenu = document.getElementById('navMenu');
                this.navOverlay = document.getElementById('navOverlay');
                this.isOpen = false;
                this.init();
            }

            init() {
                // Set up data attributes for modern event handling
                if (this.navToggle) {
                    this.navToggle.dataset.action = 'open';
                    this.navToggle.dataset.component = 'navigation';
                }
                if (this.navClose) {
                    this.navClose.dataset.action = 'close';
                    this.navClose.dataset.component = 'navigation';
                }
                if (this.navOverlay) {
                    this.navOverlay.dataset.action = 'close';
                    this.navOverlay.dataset.component = 'navigation';
                }

                // Set up nav links
                document.querySelectorAll('.nav-links a, .nav-links-desktop a').forEach(link => {
                    link.dataset.action = 'close';
                    link.dataset.component = 'navigation';
                });

                // Accessibility: capture first focusable element in menu
                if (this.navMenu) {
                    this.firstFocusable = this.navMenu.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
                }
            }

            handleAction(action, target, event) {
                switch (action) {
                    case 'open':
                        this.open();
                        break;
                    case 'close':
                        this.close();
                        break;
                    case 'toggle':
                        this.toggle();
                        break;
                }
            }

            toggle() {
                this.isOpen ? this.close() : this.open();
            }

            open() {
                if (this.navMenu && this.navOverlay) {
                    this.navMenu.classList.add('active');
                    this.navOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    this.isOpen = true;
                    // Move focus into the menu
                    setTimeout(() => this.firstFocusable?.focus(), 0);
                    // Trap focus inside menu
                    this.focusHandler = (e) => {
                        if (!this.isOpen) return;
                        const focusables = Array.from(this.navMenu.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])'));
                        const first = focusables[0];
                        const last = focusables[focusables.length - 1];
                        if (e.key === 'Tab') {
                            if (e.shiftKey && document.activeElement === first) {
                                e.preventDefault();
                                last.focus();
                            } else if (!e.shiftKey && document.activeElement === last) {
                                e.preventDefault();
                                first.focus();
                            }
                        }
                    };
                    document.addEventListener('keydown', this.focusHandler);
                }
            }

            close() {
                if (this.navMenu && this.navOverlay) {
                    this.navMenu.classList.remove('active');
                    this.navOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    this.isOpen = false;
                    // Return focus to toggle
                    this.navToggle?.focus();
                    if (this.focusHandler) {
                        document.removeEventListener('keydown', this.focusHandler);
                        this.focusHandler = null;
                    }
                }
            }
        }

        // Scroll Effects Component
        class ScrollEffectsComponent {
            constructor() {
                this.progressBar = document.getElementById('progressBar');
                this.stickyHeader = document.getElementById('stickyHeader');
                this.lastScrollY = 0;
                this.ticking = false;
            }

            update() {
                if (!this.ticking) {
                    requestAnimationFrame(() => {
                        this.updateProgressBar();
                        this.updateStickyHeader();
                        this.ticking = false;
                    });
                    this.ticking = true;
                }
            }

            updateProgressBar() {
                if (!this.progressBar) return;
                
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = Math.min((scrollTop / docHeight) * 100, 100);
                this.progressBar.style.width = `${scrollPercent}%`;
            }

            updateStickyHeader() {
                if (!this.stickyHeader) return;
                
                const scrollTop = window.pageYOffset;
                const threshold = 120;

                if (scrollTop > threshold) {
                    this.stickyHeader.classList.add('visible');
                } else {
                    this.stickyHeader.classList.remove('visible');
                }

                this.lastScrollY = scrollTop;
            }
        }

        // Animations Component
        class AnimationsComponent {
            constructor() {
                this.createNetworkAnimation();
                this.setupRippleEffects();
                this.setupTouchSwipe();
                this.setupSmoothScrolling();
                this.setupParallaxEffect();
                this.initializeHeroAnimations();
            }

            createNetworkAnimation() {
                const networkBg = document.getElementById('networkBg');
                if (!networkBg) return;

                networkBg.innerHTML = '';

                // Create floating nodes with better performance
                for (let i = 0; i < 8; i++) {
                    const node = document.createElement('div');
                    node.className = 'network-node';
                    node.style.left = `${Math.random() * 100}%`;
                    node.style.top = `${Math.random() * 100}%`;
                    node.style.animationDelay = `${Math.random() * 8}s`;
                    networkBg.appendChild(node);
                }

                // Create connecting lines
                for (let i = 0; i < 4; i++) {
                    const line = document.createElement('div');
                    line.className = 'network-line';
                    line.style.left = `${Math.random() * 100}%`;
                    line.style.top = `${Math.random() * 100}%`;
                    line.style.width = `${Math.random() * 100 + 30}px`;
                    line.style.transform = `rotate(${Math.random() * 360}deg)`;
                    line.style.animationDelay = `${Math.random() * 6}s`;
                    networkBg.appendChild(line);
                }
            }

            setupRippleEffects() {
                document.addEventListener('click', (e) => {
                    const button = e.target.closest('.ripple');
                    if (!button) return;

                    const ripple = document.createElement('span');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    Object.assign(ripple.style, {
                        width: `${size}px`,
                        height: `${size}px`,
                        left: `${x}px`,
                        top: `${y}px`
                    });
                    
                    ripple.classList.add('ripple-effect');
                    button.appendChild(ripple);

                    setTimeout(() => ripple.remove(), 600);
                });
            }

            setupTouchSwipe() {
                const swipeElements = document.querySelectorAll('.team-grid, .mentors-grid');
                
                swipeElements.forEach(element => {
                    let startX = 0;
                    let scrollLeft = 0;

                    element.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].pageX - element.offsetLeft;
                        scrollLeft = element.scrollLeft;
                    }, { passive: true });

                    element.addEventListener('touchmove', (e) => {
                        if (!startX) return;
                        const x = e.touches[0].pageX - element.offsetLeft;
                        const walk = (x - startX) * 2;
                        element.scrollLeft = scrollLeft - walk;
                    }, { passive: true });

                    element.addEventListener('touchend', () => {
                        startX = 0;
                    }, { passive: true });
                });
            }

            setupSmoothScrolling() {
                document.addEventListener('click', (e) => {
                    const anchor = e.target.closest('a[href^="#"]');
                    if (!anchor) return;

                    e.preventDefault();
                    const targetId = anchor.getAttribute('href');
                    const target = document.querySelector(targetId);
                    
                    if (target) {
                        // Use a smaller offset for footer to avoid weird animations
                        const headerOffset = targetId === '#footer' ? 20 : 80;
                        const elementPosition = target.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        // Use requestAnimationFrame for smoother animation
                        const startPosition = window.pageYOffset;
                        const distance = offsetPosition - startPosition;
                        const duration = Math.min(Math.abs(distance) / 2, 800); // Max 800ms
                        let start = null;

                        function animateScroll(timestamp) {
                            if (!start) start = timestamp;
                            const progress = timestamp - start;
                            const percentage = Math.min(progress / duration, 1);
                            
                            // Easing function for smoother animation
                            const easeOutCubic = 1 - Math.pow(1 - percentage, 3);
                            
                            window.scrollTo(0, startPosition + (distance * easeOutCubic));
                            
                            if (progress < duration) {
                                requestAnimationFrame(animateScroll);
                            }
                        }

                        requestAnimationFrame(animateScroll);
                    }
                });
            }

            setupParallaxEffect() {
                let ticking = false;
                
                const updateParallax = () => {
                    const scrolled = window.pageYOffset;
                    const parallaxElements = document.querySelectorAll('.parallax-bg');

                    parallaxElements.forEach(element => {
                        const speed = 0.3; // Reduced for better performance
                        element.style.transform = `translate3d(0, ${scrolled * speed}px, 0)`;
                    });
                    
                    ticking = false;
                };

                window.addEventListener('scroll', () => {
                    if (!ticking) {
                        requestAnimationFrame(updateParallax);
                        ticking = true;
                    }
                }, { passive: true });
            }

            initializeHeroAnimations() {
                // Stagger hero animations with better timing
                setTimeout(() => {
                    document.querySelectorAll('.hero .fade-in').forEach((el, index) => {
                        setTimeout(() => el.classList.add('visible'), index * 150);
                    });
                }, 300);
            }

            handleResize() {
                // Recreate network animation on resize for responsiveness
                this.createNetworkAnimation();
            }
        }

        // Initialize the app when DOM is loaded
        let app;

        const initializeApp = () => {
            app = new UllensCollegeApp();
            
            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('✅ Lucide icons initialized');
            }
            
            console.log('🚀 Modern Ullens College website loaded');
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }

        // Legacy function support for backward compatibility
        function openNav() { app?.components.get('navigation')?.open(); }
        function closeNav() { app?.components.get('navigation')?.close(); }
        function toggleNav() { app?.components.get('navigation')?.toggle(); }
    </script>

</body>
</html>
