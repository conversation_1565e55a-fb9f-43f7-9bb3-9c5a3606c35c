<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Join the first-ever class of cybersecurity experts at Ullens College. World-class B.Tech in Cybersecurity through partnership with Kathmandu University.">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-TN01KTVJ35"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-TN01KTVJ35');
        // Remove GA4 config for GTM container ID (GT-*) — incorrect

        // Scroll Depth Tracking
        let scrollDepths = [25, 50, 75, 90];
        let reachedDepths = new Set();

        function calculateScrollDepth() {
            const winHeight = window.innerHeight;
            const docHeight = document.documentElement.scrollHeight - winHeight;
            const scrollTop = window.scrollY;
            const scrollPercent = (scrollTop / docHeight) * 100;

            scrollDepths.forEach(depth => {
                if (scrollPercent >= depth && !reachedDepths.has(depth)) {
                    reachedDepths.add(depth);
                    gtag('event', 'scroll_depth', {
                        'depth': depth,
                        'page_title': document.title,
                        'page_location': window.location.href
                    });
                }
            });
        }

        // Track Apply Now Button Clicks
        function trackApplyNowClick(element) {
            gtag('event', 'apply_now_click', {
                'event_category': 'Engagement',
                'event_label': 'Apply Now Button Click',
                'page_title': document.title,
                'page_location': window.location.href
            });
        }

        // Add event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll tracking
            window.addEventListener('scroll', _.throttle(calculateScrollDepth, 1000));

            // Apply Now button tracking
            document.querySelectorAll('a[href*="apply"], button[data-action="apply"]').forEach(button => {
                button.addEventListener('click', function(e) {
                    trackApplyNowClick(this);
                });
            });
        });
    </script>
    <title>Ullens College | B.Tech in Cybersecurity</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/UC%20Logo.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon%20-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png">
    <link rel="manifest" href="/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#1a3c6e">
    <meta name="theme-color" content="#1a3c6e">
    
    <!-- PWA Manifest -->
    
    <!-- Preload critical resources -->
    <link rel="preload" href="UC%20Logo.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Fira+Code:wght@400;500;600&display=swap" as="style">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Fira+Code:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lodash for throttling -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/4.17.21/lodash.min.js"></script>
    
    <!-- Modern Icons - Lucide Icons CDN -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        :root {
            /* Enhanced Color System */
            --bg-color: #FFFFFF;
            --bg-secondary: #FAFBFC;
            --bg-tertiary: #F7F9FC;
            --text-color: #0F172A;
            --text-secondary: #475569;
            --text-tertiary: #64748B;
            --text-muted: #94A3B8;
            
            /* Primary Brand Colors - UC Logo Color */
            --primary-color: #c82f48;          /* UC Logo primary color */
            --primary-dark: #a02639;           /* Darker shade */
            --primary-darker: #7d1d2a;        /* Darkest shade */
            /* Link colors tuned for AA contrast on light backgrounds */
            --link-color: #a02639;
            --link-hover: #7d1d2a;
            --primary-light: #f4e6ea;         /* Light tint */
            --primary-lighter: #faf7f8;       /* Lightest tint */
            
            /* Accent Colors */
            --accent-blue: #3B82F6;
            --accent-green: #10B981;
            --accent-purple: #8B5CF6;
            --accent-orange: #F59E0B;
            
            /* Semantic Colors */
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
            
            /* Border & Surface Colors */
            --border-color: #E2E8F0;
            --border-secondary: #CBD5E1;
            --surface-elevated: #FFFFFF;
            --surface-card: #FEFEFE;
            /* Paper background (off-white) */
            --paper-bg: #FAFAF7;
            
            /* Shadows */
            --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 8px 10px rgba(0, 0, 0, 0.04);
            
            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-medium: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* Border Radius */
            --radius-xs: 4px;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 20px;
            --radius-full: 9999px;
            
            /* Spacing Scale */
            --space-1: 0.25rem;   /* 4px */
            --space-2: 0.5rem;    /* 8px */
            --space-3: 0.75rem;   /* 12px */
            --space-4: 1rem;      /* 16px */
            --space-5: 1.25rem;   /* 20px */
            --space-6: 1.5rem;    /* 24px */
            --space-8: 2rem;      /* 32px */
            --space-10: 2.5rem;   /* 40px */
            --space-12: 3rem;     /* 48px */
            --space-16: 4rem;     /* 64px */
            --space-20: 5rem;     /* 80px */
            --space-24: 6rem;     /* 96px */
            
            /* Typography Scale */
            --text-xs: 0.75rem;     /* 12px */
            --text-sm: 0.875rem;    /* 14px */
            --text-base: 1rem;      /* 16px */
            --text-lg: 1.125rem;    /* 18px */
            --text-xl: 1.25rem;     /* 20px */
            --text-2xl: 1.5rem;     /* 24px */
            --text-3xl: 1.875rem;   /* 30px */
            --text-4xl: 2.25rem;    /* 36px */
            --text-5xl: 3rem;       /* 48px */
            --text-6xl: 3.75rem;    /* 60px */
            --text-7xl: 4.5rem;     /* 72px */
            
            /* Line Heights */
            --leading-none: 1;
            --leading-tight: 1.25;
            --leading-snug: 1.375;
            --leading-normal: 1.5;
            --leading-relaxed: 1.625;
            --leading-loose: 2;
        }

        /* Modern CSS Features */
        @supports (container-type: inline-size) {
            .container {
                container-type: inline-size;
                container-name: main-container;
            }
        }

        /* Enhanced Base Styles */
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: clamp(14px, 1vw, 18px);
            line-height: var(--leading-normal);
        }

        /* Modern scroll-snap */
        .scroll-container {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh; /* fallback */
            height: 100dvh;
        }

        section {
            scroll-snap-align: start;
        }

        body {
            background-color: var(--paper-bg);
            /* Ultra-subtle paper texture */
            background-image:
                radial-gradient(80% 60% at 50% 30%, rgba(0, 0, 0, 0.02), transparent 60%),
                repeating-linear-gradient(
                    0deg,
                    rgba(0, 0, 0, 0.012) 0, rgba(0, 0, 0, 0.012) 1px,
                    transparent 1px, transparent 3px
                ),
                repeating-linear-gradient(
                    90deg,
                    rgba(0, 0, 0, 0.008) 0, rgba(0, 0, 0, 0.008) 1px,
                    transparent 1px, transparent 4px
                );
            background-blend-mode: multiply, normal, normal;
            background-size: auto, 200px 200px, 240px 240px;
            color: var(--text-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            overflow-x: hidden;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Enhanced Loading Animation */
        .loading-skeleton {
            background: linear-gradient(90deg, 
                var(--bg-secondary) 25%, 
                var(--border-color) 50%, 
                var(--bg-secondary) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite ease-in-out;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Enhanced Progress Bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-blue), var(--primary-color));
            z-index: 1000;
            transition: width var(--transition-fast);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
        }

        /* Enhanced Fade-in Animation */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity var(--transition-slow), transform var(--transition-slow);
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Background Effects */
        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 25% 25%, var(--primary-lighter) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
            z-index: -1;
            opacity: 0.6;
        }

        /* Enhanced Container */
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 var(--space-6);
            position: relative;
        }

section {
    padding: clamp(2.5rem, 6vw, 5rem) 0; /* responsive vertical rhythm */
    position: relative;
}

        /* Enhanced Vision Section */
.vision-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 50%, var(--bg-color) 100%),
                radial-gradient(circle at 50% 20%, rgba(220, 38, 38, 0.02) 0%, transparent 60%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .vision-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23dc2626' fill-opacity='0.008'%3E%3Ccircle cx='60' cy='60' r='1'/%3E%3Ccircle cx='20' cy='20' r='0.5'/%3E%3Ccircle cx='100' cy='20' r='0.5'/%3E%3Ccircle cx='20' cy='100' r='0.5'/%3E%3Ccircle cx='100' cy='100' r='0.5'/%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
            animation: visionFloat 20s ease-in-out infinite;
        }

        @keyframes visionFloat {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
            50% { transform: translateY(-8px) scale(1.05); opacity: 0.6; }
        }

        .vision-content {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
            position: relative;
            z-index: 2;
            padding: 0 var(--space-6);
        }

        .vision-section h2 {
            font-size: clamp(var(--text-4xl), 6vw, var(--text-6xl));
            font-weight: 800;
            margin-bottom: var(--space-8);
            background: linear-gradient(135deg, var(--text-color) 0%, var(--primary-color) 50%, var(--text-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.03em;
            line-height: var(--leading-tight);
            position: relative;
        }

        .vision-section h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            border-radius: var(--radius-full);
            opacity: 0.6;
        }

        .vision-statement {
            font-size: clamp(var(--text-lg), 2.5vw, var(--text-xl));
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            margin: 0;
            font-weight: 400;
            letter-spacing: -0.01em;
            position: relative;
        }

        .vision-statement::before {
            content: '"';
            position: absolute;
            top: -20px;
            left: -30px;
            font-size: 4rem;
            color: var(--primary-color);
            opacity: 0.1;
            font-family: Georgia, serif;
            line-height: 1;
        }

        .vision-statement::after {
            content: '"';
            position: absolute;
            bottom: -30px;
            right: -30px;
            font-size: 4rem;
            color: var(--primary-color);
            opacity: 0.1;
            font-family: Georgia, serif;
            line-height: 1;
        }

        .vision-highlight {
            font-weight: 500;
            color: var(--text-color);
            position: relative;
        }

        .vision-section:hover .vision-highlight {
            color: var(--primary-color);
            transition: color var(--transition-medium);
        }

        /* Subtle emphasis for key concepts */
        .vision-emphasis {
            font-weight: 500;
            color: var(--text-color);
            border-bottom: 1px solid transparent;
            transition: all var(--transition-medium);
        }

        .vision-section:hover .vision-emphasis {
            border-bottom-color: rgba(220, 38, 38, 0.2);
        }

        /* Enhanced Program Section */
.program-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 40%, var(--bg-tertiary) 60%, var(--bg-color) 100%),
                radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.02) 0%, transparent 60%),
                radial-gradient(circle at 20% 70%, rgba(220, 38, 38, 0.015) 0%, transparent 60%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .program-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='90' height='90' viewBox='0 0 90 90' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.006'%3E%3Cpath d='M15 15h10v10H15zM35 5h10v10H35zM55 25h10v10H55zM75 15h10v10H75zM25 65h10v10H25zM65 75h10v10H65z'/%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
            animation: programFloat 18s ease-in-out infinite;
        }

        @keyframes programFloat {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.25; }
            33% { transform: translateX(2px) translateY(-3px) rotate(0.3deg); opacity: 0.35; }
            66% { transform: translateX(-3px) translateY(-2px) rotate(-0.2deg); opacity: 0.4; }
        }

        .program-content {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
            position: relative;
            z-index: 2;
            padding: 0 var(--space-6);
        }

        .program-section h2 {
            font-size: clamp(var(--text-4xl), 5.5vw, var(--text-6xl));
            font-weight: 800;
            margin-bottom: var(--space-10);
            background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-blue) 30%, var(--primary-color) 70%, var(--text-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: var(--leading-tight);
            position: relative;
        }

        .program-section h2::after {
            content: '';
            position: absolute;
            bottom: -18px;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 4px;
            background: linear-gradient(90deg, transparent, var(--accent-blue), var(--primary-color), transparent);
            border-radius: var(--radius-full);
            opacity: 0.6;
        }

        .program-description {
            font-size: clamp(var(--text-lg), 2.2vw, var(--text-xl));
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            margin: 0;
            font-weight: 400;
            letter-spacing: -0.008em;
            position: relative;
            max-width: 900px;
            margin: 0 auto;
        }

        .program-description::before {
            content: '🎯';
            position: absolute;
            top: -25px;
            left: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        .program-description::after {
            content: '🚀';
            position: absolute;
            bottom: -25px;
            right: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        /* Subtle emphasis for key concepts in program */
        .program-emphasis {
            font-weight: 600;
            color: var(--text-color);
            position: relative;
            transition: all var(--transition-medium);
        }

        .program-emphasis::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
            opacity: 0;
            transform: scaleX(0);
            transition: all var(--transition-medium);
        }

        .program-section:hover .program-emphasis::after {
            opacity: 0.5;
            transform: scaleX(1);
        }

        .program-highlight {
            font-weight: 700;
            color: var(--text-color);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(220, 38, 38, 0.05));
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-sm);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all var(--transition-medium);
            display: inline-block;
            margin: 0 2px;
        }

        .program-section:hover .program-highlight {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(220, 38, 38, 0.08));
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            margin: 0;
            color: var(--text-color);
            font-weight: 600;
            letter-spacing: -0.025em;
            line-height: var(--leading-tight);
        }

        h1 {
            font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
            font-weight: 800;
            line-height: var(--leading-none);
            background: linear-gradient(135deg, var(--text-color), var(--text-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: clamp(var(--text-3xl), 4vw, var(--text-5xl));
            font-weight: 700;
            margin-bottom: var(--space-6);
            text-align: center;
        }

        h3 {
            font-size: clamp(var(--text-xl), 2.5vw, var(--text-2xl));
            font-weight: 600;
            line-height: var(--leading-snug);
        }

        h4 {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            font-weight: 500;
            line-height: var(--leading-normal);
        }

        p {
            margin: 0 0 var(--space-4) 0;
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: var(--leading-relaxed);
        }

        a {
            color: var(--text-color);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        a:hover {
            color: var(--primary-color);
        }

        a:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        strong {
            color: var(--text-color);
            font-weight: 600;
        }

        /* Enhanced Sticky Header */
        .sticky-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            z-index: 1000;
            padding: var(--space-4) 0;
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            transform: translateY(-100%);
            transition: transform var(--transition-medium);
        }

        .sticky-header.visible {
            transform: translateY(0);
        }

        .sticky-header .container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sticky-header .logo {
            height: 56px;
        }

        .sticky-header .nav-links-desktop {
            display: flex;
            gap: var(--space-6);
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .sticky-header .nav-links-desktop a {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 500;
            font-size: var(--text-sm);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }

        .sticky-header .nav-links-desktop a:hover {
            background: var(--bg-secondary);
            color: var(--primary-color);
        }

        /* Enhanced Mobile Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            z-index: 1000;
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .nav-toggle {
            display: none;
            background: none;
            border: none;
            font-size: var(--text-xl);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            color: var(--text-color);
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-toggle:hover {
            background-color: var(--bg-secondary);
        }

        .nav-toggle:focus, .cta-button:focus, .nav-links a:focus, button:focus {
            outline: 2px solid var(--accent-blue);
            outline-offset: 2px;
        }

        .nav-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 80%;
            max-width: 320px;
            height: 100vh; /* fallback */
            height: 100dvh;
            background: var(--surface-elevated);
            box-shadow: var(--shadow-xl);
            transition: right var(--transition-medium);
            padding: var(--space-6);
            z-index: 1001;
            overflow-y: auto;
        }

        .nav-menu.active {
            right: 0;
        }

        .nav-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            opacity: 0;
            visibility: hidden;
            transition: opacity var(--transition-medium), visibility var(--transition-medium);
            z-index: 1000;
        }

        .nav-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .nav-links {
            list-style: none;
            padding: 0;
            margin: var(--space-12) 0;
        }

        .nav-links li {
            margin: var(--space-2) 0;
        }

        .nav-links a {
            display: block;
            padding: var(--space-4);
            color: var(--text-color);
            text-decoration: none;
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
            font-weight: 500;
            font-size: var(--text-sm);
            min-height: 44px;
        }

        .nav-links a:hover {
            background-color: var(--bg-secondary);
            color: var(--primary-color);
            transform: translateX(4px);
        }

        /* Enhanced Hero Section - Base styling only (texture added in general section rules) */
        .hero {
            min-height: 100vh; /* fallback */
            min-height: 100dvh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%);
            animation: heroGradient 8s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes heroGradient {
            0%, 100% {
                background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%);
            }
            50% {
                background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            margin: 0 auto;
            padding: 0 var(--space-6);
        }

        .hero-logo {
            max-width: 320px;
            margin-bottom: var(--space-8);
            transition: all var(--transition-medium);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .hero-logo:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
        }

        .hero h1 {
            margin-bottom: var(--space-8);
            color: var(--text-color);
            line-height: var(--leading-tight);
        }

        .hero-tagline {
            font-size: clamp(var(--text-xl), 3vw, var(--text-2xl));
            font-weight: 600;
            color: var(--primary-color);
            margin: var(--space-8) 0 var(--space-6);
            letter-spacing: -0.02em;
            text-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
        }

        .hero-subtitle {
            font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
            font-weight: 500;
            margin: var(--space-6) 0;
            color: var(--text-secondary);
        }

        .hero-description {
            max-width: 700px;
            margin: var(--space-8) auto;
            font-size: var(--text-lg);
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
        }

        .hero-cta {
            margin-top: var(--space-10);
        }

        /* Enhanced CTA Button */
        .cta-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-xl);
            font-weight: 600;
            font-size: var(--text-base);
            letter-spacing: 0.025em;
            transition: all var(--transition-medium);
            border: none;
            cursor: pointer;
            text-decoration: none;
            min-height: 52px;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-darker));
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .cta-button:hover::before {
            opacity: 1;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .cta-button:active {
            transform: translateY(0);
            box-shadow: var(--shadow-md);
        }

        .cta-button span {
            position: relative;
            z-index: 1;
        }

        /* Enhanced Ripple Effect */
        .ripple {
            position: relative;
            overflow: hidden;
        }

        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced Network Background */
        .network-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.03;
        }

        .network-node {
            position: absolute;
            width: 3px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }

        .network-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            opacity: 0.2;
            animation: pulse 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-15px) rotate(180deg); opacity: 0.6; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.1; transform: scaleX(0.8); }
            50% { opacity: 0.3; transform: scaleX(1.2); }
        }

        /* Enhanced Team Section */
.team-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 35%, var(--bg-tertiary) 65%, var(--bg-color) 100%),
                radial-gradient(circle at 25% 75%, rgba(16, 185, 129, 0.015) 0%, transparent 60%),
                radial-gradient(circle at 75% 25%, rgba(220, 38, 38, 0.02) 0%, transparent 60%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .team-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2310b981' fill-opacity='0.008'%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3Ccircle cx='60' cy='20' r='1.5'/%3E%3Ccircle cx='20' cy='60' r='1.5'/%3E%3Ccircle cx='60' cy='60' r='2'/%3E%3Ccircle cx='40' cy='10' r='1'/%3E%3Ccircle cx='10' cy='40' r='1'/%3E%3Ccircle cx='70' cy='40' r='1'/%3E%3Ccircle cx='40' cy='70' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.015) 50%, transparent 100%);
            pointer-events: none;
            animation: teamFloat 20s ease-in-out infinite;
        }

        @keyframes teamFloat {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.3; }
            25% { transform: translateX(1px) translateY(-2px) rotate(0.2deg); opacity: 0.4; }
            50% { transform: translateX(-1px) translateY(-3px) rotate(-0.1deg); opacity: 0.5; }
            75% { transform: translateX(-2px) translateY(1px) rotate(0.1deg); opacity: 0.4; }
        }

        .team-intro-container {
            text-align: center;
            max-width: 900px;
            margin: 0 auto var(--space-16) auto;
            position: relative;
            z-index: 2;
            padding: 0 var(--space-6);
        }

        .team-section h2 {
            font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
            font-weight: 800;
            margin-bottom: var(--space-8);
            background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-green) 40%, var(--primary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: var(--leading-tight);
            position: relative;
        }

        .team-section h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--accent-green), var(--primary-color), transparent);
            border-radius: var(--radius-full);
            opacity: 0.6;
        }

        .team-intro {
            font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            margin: 0;
            font-weight: 400;
            letter-spacing: -0.005em;
            position: relative;
        }

        .team-intro::before {
            content: '👥';
            position: absolute;
            top: -25px;
            left: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        .team-intro::after {
            content: '🎓';
            position: absolute;
            bottom: -25px;
            right: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        /* Subtle emphasis for team description */
        .team-emphasis {
            font-weight: 500;
            color: var(--text-color);
            position: relative;
            transition: color var(--transition-medium);
        }

        .team-section:hover .team-emphasis {
            color: var(--accent-green);
        }

        /* Enhanced Future Section */
.future-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 45%, var(--bg-tertiary) 55%, var(--bg-color) 100%),
                radial-gradient(circle at 15% 85%, rgba(245, 158, 11, 0.02) 0%, transparent 60%),
                radial-gradient(circle at 85% 15%, rgba(59, 130, 246, 0.015) 0%, transparent 60%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .future-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.006'%3E%3Cpath d='M10 10l5 5-5 5-5-5zM30 20l4 4-4 4-4-4zM60 10l6 6-6 6-6-6zM80 30l3 3-3 3-3-3zM20 70l4 4-4 4-4-4zM70 80l5 5-5 5-5-5z'/%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
            animation: futureFloat 22s ease-in-out infinite;
        }

        @keyframes futureFloat {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.3; }
            30% { transform: translateX(2px) translateY(-1px) rotate(0.3deg); opacity: 0.4; }
            60% { transform: translateX(-1px) translateY(-3px) rotate(-0.2deg); opacity: 0.5; }
            90% { transform: translateX(-2px) translateY(1px) rotate(0.1deg); opacity: 0.4; }
        }

        .future-intro-container {
            text-align: center;
            max-width: 900px;
            margin: 0 auto var(--space-16) auto;
            position: relative;
            z-index: 2;
            padding: 0 var(--space-6);
        }

        .future-section h2 {
            font-size: clamp(var(--text-4xl), 5.5vw, var(--text-6xl));
            font-weight: 800;
            margin-bottom: var(--space-8);
            background: linear-gradient(135deg, var(--text-color) 0%, var(--accent-orange) 35%, var(--accent-blue) 70%, var(--text-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: var(--leading-tight);
            position: relative;
        }

        .future-section h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--accent-orange), var(--accent-blue), transparent);
            border-radius: var(--radius-full);
            opacity: 0.6;
        }

        .future-intro {
            font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            margin: 0 auto var(--space-12) auto;
            font-weight: 400;
            letter-spacing: -0.005em;
            max-width: 800px;
            position: relative;
        }

        .future-intro::before {
            content: '🚀';
            position: absolute;
            top: -25px;
            left: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        .future-intro::after {
            content: '🌟';
            position: absolute;
            bottom: -25px;
            right: -40px;
            font-size: 2.5rem;
            opacity: 0.1;
            line-height: 1;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--space-10);
            margin-top: var(--space-16);
            position: relative;
            z-index: 1;
        }

        .team-card {
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%),
                radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.05), transparent 70%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: var(--space-10);
            border-radius: var(--radius-2xl);
            text-align: center;
            transition: all var(--transition-medium);
            position: relative;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.05),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            overflow: hidden;
        }

        .team-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(220, 38, 38, 0.08) 0%, 
                rgba(59, 130, 246, 0.04) 50%, 
                rgba(16, 185, 129, 0.06) 100%);
            opacity: 0;
            transition: opacity var(--transition-medium);
            border-radius: var(--radius-2xl);
        }

        .team-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-blue), var(--accent-green));
            border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .team-card:hover::before {
            opacity: 1;
        }

        .team-card:hover::after {
            opacity: 1;
        }

        .team-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border-color: rgba(220, 38, 38, 0.2);
        }

        .team-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6) auto;
            font-size: var(--text-2xl);
            color: white;
            font-weight: 700;
            transition: all var(--transition-medium);
            box-shadow: 
                0 8px 20px rgba(220, 38, 38, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
            border: 3px solid rgba(255, 255, 255, 0.9);
        }

        .team-card:hover .team-avatar {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 
                0 12px 30px rgba(220, 38, 38, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .team-card h3 {
            font-size: var(--text-xl);
            color: var(--text-color);
            margin-bottom: var(--space-2);
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        .team-role {
            color: var(--text-tertiary);
            font-weight: 500;
            margin: 0 0 var(--space-6) 0;
            font-size: var(--text-base);
            position: relative;
            z-index: 2;
        }

        .team-link {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            padding: var(--space-3) var(--space-6);
            border: 2px solid var(--primary-color);
            border-radius: var(--radius-xl);
            transition: all var(--transition-medium);
            font-size: var(--text-sm);
            position: relative;
            z-index: 2;
            overflow: hidden;
        }

        .team-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            transition: left var(--transition-medium);
            z-index: -1;
        }

        .team-link:hover::before {
            left: 0;
        }

        .team-link:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 38, 38, 0.3);
        }

        .team-link i {
            width: 16px;
            height: 16px;
            transition: transform var(--transition-fast);
        }

        .team-link:hover i {
            transform: scale(1.1);
        }

        /* Enhanced Minimalist Values Section */
.values-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 30%, var(--bg-tertiary) 70%, var(--bg-color) 100%),
                radial-gradient(circle at 30% 80%, rgba(220, 38, 38, 0.015) 0%, transparent 60%),
                radial-gradient(circle at 70% 20%, rgba(59, 130, 246, 0.01) 0%, transparent 60%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .values-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23dc2626' fill-opacity='0.005'%3E%3Cpath d='M10 10h5v5h-5zM25 25h5v5h-5zM40 10h5v5h-5zM70 25h5v5h-5zM85 70h5v5h-5zM55 85h5v5h-5z'/%3E%3C/g%3E%3C/svg%3E");
            pointer-events: none;
            animation: valuesFloat 25s ease-in-out infinite;
        }

        @keyframes valuesFloat {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.2; }
            25% { transform: translateX(3px) translateY(-2px) rotate(0.5deg); opacity: 0.3; }
            50% { transform: translateX(-2px) translateY(-3px) rotate(-0.3deg); opacity: 0.4; }
            75% { transform: translateX(-3px) translateY(1px) rotate(0.2deg); opacity: 0.3; }
        }

        .values-intro {
            text-align: center;
            max-width: 800px;
            margin: 0 auto var(--space-16) auto;
            position: relative;
            z-index: 2;
            padding: 0 var(--space-6);
        }

        .values-intro h2 {
            margin-bottom: var(--space-8);
            font-weight: 800;
            font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
            background: linear-gradient(135deg, var(--text-color) 0%, var(--primary-color) 50%, var(--text-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: var(--leading-tight);
            position: relative;
        }

        .values-intro h2::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--primary-color), var(--accent-blue), transparent);
            border-radius: var(--radius-full);
            opacity: 0.5;
        }

        .values-subtitle {
            font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
            color: var(--text-secondary);
            line-height: var(--leading-relaxed);
            margin: 0;
            font-weight: 400;
            letter-spacing: -0.005em;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
            gap: var(--space-10);
            margin-top: var(--space-16);
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Container Queries for Values Grid */
        @container main-container (min-width: 768px) {
            .values-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-12);
            }
        }

        @container main-container (min-width: 1200px) {
            .values-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: var(--space-10);
            }
        }

        .value-card {
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.92) 100%),
                radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.02), transparent 70%);
            border: 1px solid rgba(255, 255, 255, 0.6);
            padding: var(--space-12);
            border-radius: var(--radius-2xl);
            transition: all var(--transition-medium);
            position: relative;
            box-shadow: 
                0 2px 8px rgba(0, 0, 0, 0.02),
                0 8px 24px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            text-align: center;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            overflow: hidden;
        }

        .value-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.05) 0%, 
                transparent 50%, 
                rgba(255, 255, 255, 0.02) 100%);
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .value-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--primary-color), var(--accent-blue), transparent);
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .value-card:hover::before {
            opacity: 1;
        }

        .value-card:hover::after {
            opacity: 0.6;
        }

        .value-card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow: 
                0 8px 20px rgba(0, 0, 0, 0.04),
                0 20px 40px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(220, 38, 38, 0.1);
        }

        .value-icon {
            width: 72px;
            height: 72px;
            margin: 0 auto var(--space-6) auto;
            background: 
                linear-gradient(135deg, var(--primary-color), var(--primary-dark)),
                linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-medium);
            flex-shrink: 0;
            box-shadow: 
                0 4px 12px rgba(220, 38, 38, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 2;
        }

        .value-icon::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-blue));
            border-radius: var(--radius-2xl);
            opacity: 0;
            transition: opacity var(--transition-medium);
            z-index: -1;
        }

        .value-icon i {
            width: 32px;
            height: 32px;
            color: white;
            transition: transform var(--transition-fast);
        }

        .value-card:hover .value-icon {
            transform: scale(1.08) rotate(3deg);
            box-shadow: 
                0 12px 28px rgba(220, 38, 38, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .value-card:hover .value-icon::before {
            opacity: 1;
        }

        .value-card:hover .value-icon i {
            transform: scale(1.05);
        }

        .value-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .value-card h3 {
            color: var(--text-color);
            font-size: var(--text-xl);
            margin-bottom: var(--space-5);
            font-weight: 700;
            line-height: var(--leading-tight);
            letter-spacing: -0.01em;
        }

        .value-card p {
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            font-size: var(--text-base);
            margin: 0;
            flex: 1;
            letter-spacing: -0.003em;
        }

        /* Subtle emphasis for key concepts in values */
        .value-emphasis {
            font-weight: 500;
            color: var(--text-color);
            position: relative;
            transition: color var(--transition-medium);
        }

        .value-card:hover .value-emphasis {
            color: var(--primary-color);
        }

        /* Enhanced Advantage Section */
.advantage-section {
            background: 
                linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 30%, var(--bg-tertiary) 70%, var(--bg-color) 100%),
                radial-gradient(circle at 10% 20%, rgba(220, 38, 38, 0.04) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    padding: clamp(2.5rem, 7vw, 6rem) 0;
    margin: 0;
            position: relative;
            overflow: hidden;
        }

        .advantage-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23dc2626' fill-opacity='0.02'%3E%3Cpath d='M20 20h20v20H20zM60 60h20v20H60z'/%3E%3C/g%3E%3C/svg%3E"),
                linear-gradient(45deg, transparent 49%, rgba(220, 38, 38, 0.01) 50%, transparent 51%);
            pointer-events: none;
            animation: advantageFloat 15s ease-in-out infinite;
        }

        @keyframes advantageFloat {
            0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.3; }
            25% { transform: translateX(5px) translateY(-3px) rotate(0.5deg); opacity: 0.4; }
            50% { transform: translateX(-3px) translateY(-5px) rotate(-0.3deg); opacity: 0.5; }
            75% { transform: translateX(-5px) translateY(2px) rotate(0.2deg); opacity: 0.4; }
        }

        .advantage-intro {
            text-align: center;
            max-width: 900px;
            margin: 0 auto var(--space-16) auto;
            position: relative;
            z-index: 2;
        }

        .advantage-intro h2 {
            margin-bottom: var(--space-6);
            background: linear-gradient(135deg, var(--text-color), var(--primary-color), var(--accent-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .advantage-intro::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-blue), var(--accent-green), var(--accent-orange));
            border-radius: var(--radius-full);
        }

        .advantage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
            gap: var(--space-10);
            margin-top: var(--space-16);
            position: relative;
            z-index: 2;
        }

        .advantage-item {
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%),
                radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.05), transparent 70%);
            border: 1px solid rgba(255, 255, 255, 0.4);
            padding: var(--space-10);
            border-radius: var(--radius-2xl);
            transition: all var(--transition-medium);
            position: relative;
            text-align: center;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.06),
                0 2px 8px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            overflow: hidden;
        }

        /* Unique styling for each advantage item */
        .advantage-item:nth-child(1) {
            background: 
                linear-gradient(135deg, rgba(220, 38, 38, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
                radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.1), transparent 70%);
        }

        .advantage-item:nth-child(2) {
            background: 
                linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
                radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1), transparent 70%);
        }

        .advantage-item:nth-child(3) {
            background: 
                linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
                radial-gradient(circle at 30% 30%, rgba(16, 185, 129, 0.1), transparent 70%);
        }

        .advantage-item:nth-child(4) {
            background: 
                linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
                radial-gradient(circle at 30% 30%, rgba(245, 158, 11, 0.1), transparent 70%);
        }

        .advantage-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                transparent 50%, 
                rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .advantage-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            opacity: 0;
            transition: all var(--transition-medium);
            border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
        }

        .advantage-item:nth-child(1)::after { background: linear-gradient(90deg, var(--primary-color), var(--primary-dark)); }
        .advantage-item:nth-child(2)::after { background: linear-gradient(90deg, var(--accent-blue), #1e40af); }
        .advantage-item:nth-child(3)::after { background: linear-gradient(90deg, var(--accent-green), #047857); }
        .advantage-item:nth-child(4)::after { background: linear-gradient(90deg, var(--accent-orange), #d97706); }

        .advantage-item:hover::before {
            opacity: 1;
        }

        .advantage-item:hover::after {
            opacity: 1;
        }

        .advantage-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 20px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        .advantage-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--space-6) auto;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            transition: all var(--transition-medium);
            box-shadow: 
                0 8px 20px rgba(220, 38, 38, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;
        }

        /* Unique icon backgrounds */
        .advantage-item:nth-child(1) .advantage-icon {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            box-shadow: 0 8px 20px rgba(220, 38, 38, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .advantage-item:nth-child(2) .advantage-icon {
            background: linear-gradient(135deg, var(--accent-blue), #1e40af);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .advantage-item:nth-child(3) .advantage-icon {
            background: linear-gradient(135deg, var(--accent-green), #047857);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .advantage-item:nth-child(4) .advantage-icon {
            background: linear-gradient(135deg, var(--accent-orange), #d97706);
            box-shadow: 0 8px 20px rgba(245, 158, 11, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .advantage-item:hover .advantage-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 
                0 12px 30px rgba(220, 38, 38, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .advantage-item:nth-child(2):hover .advantage-icon {
            box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .advantage-item:nth-child(3):hover .advantage-icon {
            box-shadow: 0 12px 30px rgba(16, 185, 129, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .advantage-item:nth-child(4):hover .advantage-icon {
            box-shadow: 0 12px 30px rgba(245, 158, 11, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .advantage-content {
            position: relative;
            z-index: 2;
        }

        .advantage-item h3 {
            margin-bottom: var(--space-5);
            color: var(--text-color);
            font-size: var(--text-xl);
            font-weight: 700;
            line-height: var(--leading-tight);
        }

        .advantage-emphasis {
            font-weight: 600;
            color: var(--text-color);
            position: relative;
            transition: all var(--transition-medium);
        }

        .advantage-emphasis::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            opacity: 0;
            transform: scaleX(0);
            transition: all var(--transition-medium);
        }

        .advantage-item:hover .advantage-emphasis::after {
            opacity: 0.6;
            transform: scaleX(1);
        }

        /* Unique emphasis colors for each card */
        .advantage-item:nth-child(1) .advantage-emphasis {
            color: var(--text-color);
        }

        .advantage-item:nth-child(1):hover .advantage-emphasis::after {
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        }

        .advantage-item:nth-child(2) .advantage-emphasis {
            color: var(--text-color);
        }

        .advantage-item:nth-child(2):hover .advantage-emphasis::after {
            background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
        }

        .advantage-item:nth-child(3) .advantage-emphasis {
            color: var(--text-color);
        }

        .advantage-item:nth-child(3):hover .advantage-emphasis::after {
            background: linear-gradient(90deg, transparent, var(--accent-green), transparent);
        }

        .advantage-item:nth-child(4) .advantage-emphasis {
            color: var(--text-color);
        }

        .advantage-item:nth-child(4):hover .advantage-emphasis::after {
            background: linear-gradient(90deg, transparent, var(--accent-orange), transparent);
        }

        /* Alternative: Typography-based emphasis */
        .advantage-key-point {
            font-weight: 650;
            letter-spacing: -0.01em;
            color: var(--text-color);
        }

        .advantage-item p {
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            font-size: var(--text-base);
            margin: 0;
        }

        /* Enhanced Future Grid */
        .future-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--space-12);
            margin-top: var(--space-8);
            position: relative;
            z-index: 2;
        }

        .future-card {
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.92) 100%),
                radial-gradient(circle at 30% 30%, rgba(245, 158, 11, 0.03), transparent 70%);
            border: 1px solid rgba(255, 255, 255, 0.6);
            padding: var(--space-12);
            border-radius: var(--radius-2xl);
            transition: all var(--transition-medium);
            position: relative;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.04),
                0 2px 8px rgba(0, 0, 0, 0.03),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            overflow: hidden;
        }

        .future-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(245, 158, 11, 0.05) 0%, 
                transparent 50%, 
                rgba(59, 130, 246, 0.03) 100%);
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .future-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--accent-orange), var(--accent-blue), transparent);
            opacity: 0;
            transition: opacity var(--transition-medium);
        }

        .future-card:hover::before {
            opacity: 1;
        }

        .future-card:hover::after {
            opacity: 0.6;
        }

        .future-card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow: 
                0 12px 30px rgba(0, 0, 0, 0.06),
                0 6px 16px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(245, 158, 11, 0.15);
        }

        .future-card h3 {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: var(--space-6);
            letter-spacing: -0.01em;
            position: relative;
            z-index: 2;
        }

        .future-card p {
            font-size: var(--text-base);
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            margin-bottom: var(--space-4);
            position: relative;
            z-index: 2;
        }

        .future-card p strong {
            color: var(--text-color);
            font-weight: 600;
        }

        .future-grid ul {
            padding-left: 0;
            list-style: none;
            margin: var(--space-4) 0 0 0;
            position: relative;
            z-index: 2;
        }

        .future-grid li {
            position: relative;
            margin-bottom: var(--space-3);
            padding-left: var(--space-8);
            font-size: var(--text-base);
            line-height: var(--leading-relaxed);
            color: var(--text-secondary);
            transition: all var(--transition-fast);
        }

        .future-grid li::before {
            content: '✓';
            position: absolute;
            left: 0;
            top: 0;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, var(--accent-orange), var(--accent-blue));
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-sm);
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
        }

        .future-card:hover li {
            color: var(--text-color);
            transform: translateX(4px);
        }

        /* Subtle emphasis for future content */
        .future-emphasis {
            font-weight: 500;
            color: var(--text-color);
            position: relative;
            transition: color var(--transition-medium);
        }

        .future-card:hover .future-emphasis {
            color: var(--accent-orange);
        }

        /* Modern Minimal Accordion */
        .accordion-item {
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin-bottom: var(--space-4);
            transition: all var(--transition-medium);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .accordion-item:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary-light);
        }

        .accordion-button {
            background: none;
            border: none;
            width: 100%;
            text-align: left;
            padding: var(--space-8);
            font-size: var(--text-base);
            font-weight: 500;
            color: var(--text-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all var(--transition-fast);
            min-height: 48px;
        }

        .accordion-button:hover {
            color: var(--primary-color);
        }

        .accordion-button::after {
            content: '+';
            font-size: var(--text-xl);
            transition: transform var(--transition-medium);
            color: var(--text-tertiary);
            font-weight: 300;
        }

        .accordion-item.active .accordion-button::after {
            transform: rotate(45deg);
            color: var(--primary-color);
        }

        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height var(--transition-medium) ease-out;
        }

        .accordion-content-inner {
            padding: 0 var(--space-8) var(--space-8) var(--space-8);
            color: var(--text-tertiary);
            line-height: var(--leading-relaxed);
        }

        /* Enhanced Scholarship Section */
        .scholarship {
            background: 
                linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(185, 28, 28, 0.08) 100%),
                radial-gradient(circle at 30% 40%, rgba(220, 38, 38, 0.03), transparent 70%),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                linear-gradient(to bottom, var(--bg-color), #fefefe);
            text-align: center;
            padding: var(--space-24) var(--space-10);
            margin: var(--space-24) 0;
            position: relative;
            overflow: hidden;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.6),
                0 2px 4px rgba(0, 0, 0, 0.02),
                0 8px 16px rgba(0, 0, 0, 0.04);
        }

        .scholarship::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(220, 38, 38, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
            pointer-events: none;
        }

        .scholarship-content {
            position: relative;
            z-index: 2;
            max-width: 1000px;
            margin: 0 auto;
        }

        .scholarship-icon {
            width: 80px;
            height: 80px;
            color: var(--primary-color);
            margin: 0 auto var(--space-6) auto;
            filter: drop-shadow(0 2px 4px rgba(220, 38, 38, 0.1));
            opacity: 0.8;
        }

        .scholarship h2 {
            color: var(--text-color);
            margin-bottom: var(--space-8);
            font-size: clamp(var(--text-3xl), 5vw, var(--text-5xl));
            font-weight: 800;
            line-height: var(--leading-tight);
            background: linear-gradient(135deg, var(--text-color), var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .scholarship-subtitle {
            font-size: var(--text-xl);
            color: var(--text-secondary);
            margin-bottom: var(--space-8);
            font-weight: 500;
        }

        .scholarship-description {
            max-width: 800px;
            margin: 0 auto var(--space-10) auto;
            color: var(--text-secondary);
            font-size: var(--text-lg);
            line-height: var(--leading-relaxed);
        }

        .scholarship-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-8);
            margin: var(--space-12) 0;
        }

        .scholarship-feature {
            background: rgba(255, 255, 255, 0.7);
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            transition: all var(--transition-medium);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .scholarship-feature:hover {
            background: rgba(255, 255, 255, 0.85);
            transform: translateY(-4px);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            color: var(--primary-color);
            margin: 0 auto var(--space-4) auto;
            opacity: 0.8;
        }

        .scholarship-feature h3 {
            color: var(--text-color);
            font-size: var(--text-xl);
            margin-bottom: var(--space-3);
            font-weight: 600;
        }

        .scholarship-feature p {
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: var(--leading-relaxed);
            margin: 0;
        }

        .scholarship-highlight {
            background: rgba(220, 38, 38, 0.08);
            padding: var(--space-6) var(--space-8);
            border-radius: var(--radius-xl);
            margin: var(--space-10) auto;
            max-width: 600px;
            border: 2px solid rgba(220, 38, 38, 0.15);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.5),
                0 4px 12px rgba(220, 38, 38, 0.05);
        }

        .scholarship-highlight .highlight-title {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-3);
        }

        .scholarship-highlight .highlight-text {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            margin: 0;
        }

        .scholarship-cta {
            margin-top: var(--space-10);
        }

        .scholarship-cta .cta-button {
            background: var(--primary-color);
            color: white;
            border: 2px solid var(--primary-color);
            font-weight: 700;
            padding: var(--space-5) var(--space-10);
            font-size: var(--text-lg);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 4px 12px rgba(220, 38, 38, 0.2);
        }

        .scholarship-cta .cta-button:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 8px 25px rgba(220, 38, 38, 0.3);
        }

        /* Final CTA & Footer */
        .final-cta {
            text-align: center;
        }
        /* Enhanced Modern Footer */
        footer {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
            padding: clamp(3rem, 6vw, 4rem) 0 clamp(2rem, 4vw, 3rem) 0; /* 48–64px top, 32–48px bottom */
            margin-top: clamp(2rem, 4vw, 3rem);
            border-top: 1px solid var(--border-color);
            position: relative;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.05), transparent 60%);
            pointer-events: none;
        }

        .footer-content {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 var(--space-6);
            display: grid;
            grid-template-columns: 1fr 300px; /* Fixed width for right column */
            gap: var(--space-8); /* Reduced gap */
            align-items: start;
            position: relative;
            z-index: 1;
        }

        .footer-main {
            text-align: left;
        }

        .footer-logo {
            max-width: 130px; /* Slightly smaller */
            margin-bottom: var(--space-3); /* Reduced margin */
            opacity: 0.9;
            transition: all var(--transition-fast);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .footer-logo:hover {
            opacity: 1;
            transform: scale(1.05);
        }

        .footer-main h3 {
            color: var(--text-color);
            font-size: var(--text-xl);
            margin-bottom: var(--space-3); /* Reduced margin */
            font-weight: 700;
            background: linear-gradient(135deg, var(--text-color), var(--primary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-description {
            color: var(--text-secondary);
            font-size: var(--text-base);
            line-height: var(--leading-relaxed);
            margin-bottom: var(--space-4); /* Reduced margin */
            max-width: 500px;
        }

        .footer-contact {
            display: grid;
            gap: var(--space-2); /* Tighter spacing */
            margin-bottom: var(--space-5); /* Reduced margin */
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-2) 0; /* Reduced padding */
            transition: all var(--transition-fast);
            border-radius: var(--radius-md);
        }

        .contact-item:hover {
            background: rgba(255, 255, 255, 0.5);
            padding-left: var(--space-3);
        }

        .contact-icon {
            width: 20px;
            height: 20px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .contact-item p {
            margin: 0;
            color: var(--text-secondary);
            font-size: var(--text-sm);
            font-weight: 500;
        }

        .contact-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color var(--transition-fast);
        }

        .contact-item a:hover {
            color: var(--primary-color);
        }

        .footer-social {
            display: flex;
            gap: var(--space-3); /* Tighter spacing */
            margin-top: var(--space-3); /* Reduced margin */
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px; /* Smaller icons */
            height: 40px;
            background: var(--surface-elevated);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            transition: all var(--transition-medium);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
        }

        .social-link:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .social-link i {
            width: 20px;
            height: 20px;
            color: var(--text-secondary);
            transition: color var(--transition-fast);
        }

        .social-link:hover i {
            color: white;
        }

        .footer-cta {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.9);
            padding: var(--space-6); /* Reduced padding */
            border-radius: var(--radius-2xl);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-md); /* Reduced shadow */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .footer-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-lighter), rgba(59, 130, 246, 0.05));
            opacity: 0.5;
        }

        .footer-cta > * {
            position: relative;
            z-index: 1;
        }

        .footer-cta h4 {
            color: var(--text-color);
            font-size: var(--text-lg); /* Smaller font */
            margin-bottom: var(--space-3); /* Reduced margin */
            font-weight: 700;
        }

        .footer-cta p {
            color: var(--text-secondary);
            margin-bottom: var(--space-5); /* Reduced margin */
            font-size: var(--text-sm); /* Smaller font */
            line-height: var(--leading-relaxed);
        }

        .footer-bottom {
            text-align: center;
            margin-top: var(--space-8); /* Reduced margin */
            padding-top: var(--space-5); /* Reduced padding */
            border-top: 1px solid var(--border-color);
            color: var(--text-tertiary);
            font-size: var(--text-sm);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin: 0;
            opacity: 0.8;
        }

        /* Modern Mobile Responsive Design */
        @media (max-width: 1024px) {
            .container {
                padding: 0 var(--space-6);
            }

            section {
                padding: var(--space-20) 0;
            }

            .values-section,
            .advantage-section,
            .team-section {
                padding: var(--space-20) 0;
                margin: var(--space-20) 0;
            }

            .values-grid,
            .advantage-grid {
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: var(--space-6);
            }

            .team-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: var(--space-6);
            }
        }

        @media (max-width: 768px) {
            /* Hide desktop header on mobile to avoid double bars */
            .sticky-header { display: none; }

            .sticky-header .nav-links-desktop {
                display: none;
            }

            .sticky-header .cta-button {
                display: none;
            }

            .sticky-header .container {
                justify-content: space-between;
            }

            /* Ensure no pseudo-element hamburger on hidden header */
            .sticky-header::after { content: none; }

            .mobile-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .nav-toggle {
                display: block;
            }

            .hero {
                min-height: 100vh; /* fallback */
                min-height: 100dvh;
                padding: var(--space-20) 0;
            }

            /* Tone down textures/gradients for readability */
            body { background-image: none; }
            .vision-section { background: var(--bg-color); }

            .hero-content {
                padding: 0 var(--space-4);
            }

            .hero-logo {
                max-width: 220px;
                margin-bottom: var(--space-4);
            }

            .hero-tagline {
                font-size: var(--text-xl);
            }

            .hero-subtitle {
                font-size: var(--text-lg);
            }

            .hero-description {
                font-size: var(--text-base);
                margin: var(--space-4) auto;
            }

            .cta-button {
                padding: var(--space-4) var(--space-8);
                font-size: var(--text-base);
            }

            .vision-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }

            .vision-content {
                padding: 0 var(--space-4);
            }

            .vision-section h2 {
                font-size: clamp(var(--text-3xl), 8vw, var(--text-5xl));
                margin-bottom: var(--space-6);
            }

            .vision-statement {
                font-size: clamp(var(--text-base), 4vw, var(--text-lg));
            }

            .vision-statement::before,
            .vision-statement::after {
                font-size: 2.5rem;
            }

            .vision-statement::before {
                top: -15px;
                left: -20px;
            }

            .vision-statement::after {
                bottom: -20px;
                right: -20px;
            }

            .program-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }

            .program-content {
                padding: 0 var(--space-4);
            }

            .program-section h2 {
                font-size: clamp(var(--text-3xl), 7vw, var(--text-5xl));
                margin-bottom: var(--space-8);
            }

            .program-section h2::after {
                width: 100px;
                height: 3px;
                bottom: -15px;
            }

            .program-description {
                font-size: clamp(var(--text-base), 3vw, var(--text-lg));
            }

            .program-description::before,
            .program-description::after {
                font-size: 2rem;
            }

            .program-description::before {
                top: -20px;
                left: -30px;
            }

            .program-description::after {
                bottom: -20px;
                right: -30px;
            }

            .team-intro-container {
                padding: 0 var(--space-4);
                margin-bottom: var(--space-12);
            }

            .team-section h2 {
                font-size: clamp(var(--text-3xl), 7vw, var(--text-5xl));
                margin-bottom: var(--space-6);
            }

            .team-section h2::after {
                width: 80px;
                height: 2px;
                bottom: -12px;
            }

            .team-intro {
                font-size: clamp(var(--text-base), 3vw, var(--text-lg));
            }

            .team-intro::before,
            .team-intro::after {
                font-size: 2rem;
            }

            .team-intro::before {
                top: -20px;
                left: -30px;
            }

            .team-intro::after {
                bottom: -20px;
                right: -30px;
            }

            .future-intro-container {
                padding: 0 var(--space-4);
                margin-bottom: var(--space-12);
            }

            .future-section h2 {
                font-size: clamp(var(--text-3xl), 7vw, var(--text-5xl));
                margin-bottom: var(--space-6);
            }

            .future-section h2::after {
                width: 100px;
                height: 2px;
                bottom: -12px;
            }

            .future-intro {
                font-size: clamp(var(--text-base), 3vw, var(--text-lg));
            }

            .future-intro::before,
            .future-intro::after {
                font-size: 2rem;
            }

            .future-intro::before {
                top: -20px;
                left: -30px;
            }

            .future-intro::after {
                bottom: -20px;
                right: -30px;
            }

            .future-grid {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }

            .future-card {
                padding: var(--space-10);
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }

            .footer-main {
                text-align: left;
            }

            .footer-logo {
                max-width: 110px;
                margin-bottom: var(--space-2);
            }

            .footer-description {
                margin-bottom: var(--space-3);
                font-size: var(--text-sm);
            }

            .contact-item {
                padding: var(--space-1) 0;
            }

            .contact-item:hover {
                padding-left: 0;
                background: transparent;
            }

            .contact-icon {
                width: 16px;
                height: 16px;
            }

            .footer-contact {
                gap: var(--space-1);
                margin-bottom: var(--space-3);
            }

            .footer-social {
                justify-content: flex-start;
                flex-wrap: wrap;
                gap: var(--space-2);
            }

            .social-link {
                width: 36px;
                height: 36px;
            }

            .footer-cta {
                padding: var(--space-5);
                text-align: center;
            }

            .footer-bottom {
                margin-top: var(--space-6);
                text-align: center;
                padding: var(--space-4) var(--space-3) 0;
            }

            /* Scholarship section mobile */
            .scholarship {
                padding: var(--space-20) var(--space-6);
                margin: var(--space-20) 0;
            }

            .scholarship-icon {
                width: 64px;
                height: 64px;
                margin-bottom: var(--space-4);
            }

            .scholarship h2 {
                font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
                margin-bottom: var(--space-6);
            }

            .scholarship-subtitle {
                font-size: var(--text-lg);
                margin-bottom: var(--space-6);
            }

            .scholarship-description {
                font-size: var(--text-base);
                margin-bottom: var(--space-8);
            }

            .scholarship-features {
                grid-template-columns: 1fr;
                gap: var(--space-6);
                margin: var(--space-10) 0;
            }

            .scholarship-feature {
                padding: var(--space-6);
            }

            .feature-icon {
                width: 40px;
                height: 40px;
                margin-bottom: var(--space-3);
            }

            .scholarship-highlight {
                padding: var(--space-5) var(--space-6);
                margin: var(--space-8) auto;
            }

            .scholarship-highlight .highlight-title {
                font-size: var(--text-xl);
                margin-bottom: var(--space-2);
            }

            .scholarship-highlight .highlight-text {
                font-size: var(--text-base);
            }

            .scholarship-cta .cta-button {
                padding: var(--space-4) var(--space-8);
                font-size: var(--text-base);
            }

            .cta-button {
                padding: var(--space-4) var(--space-8);
                font-size: var(--text-base);
            }

            .future-grid {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }

            .future-card {
                padding: var(--space-6);
            }

            .values-section,
            .advantage-section,
            .team-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }

            .values-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }

            .values-intro {
                margin-bottom: var(--space-12);
                padding: 0 var(--space-4);
            }

            .values-intro h2 {
                font-size: clamp(var(--text-3xl), 7vw, var(--text-5xl));
                margin-bottom: var(--space-6);
            }

            .values-intro h2::after {
                width: 80px;
                height: 2px;
                bottom: -10px;
            }

            .values-subtitle {
                font-size: clamp(var(--text-base), 3vw, var(--text-lg));
            }

            .values-grid,
            .advantage-grid {
                grid-template-columns: 1fr;
                gap: var(--space-8);
            }

            .value-card {
                padding: var(--space-10);
            }

            .value-icon {
                width: 64px;
                height: 64px;
                margin-bottom: var(--space-5);
            }

            .value-icon i {
                width: 28px;
                height: 28px;
            }

            .value-card h3 {
                font-size: var(--text-lg);
                margin-bottom: var(--space-4);
            }

            .value-emphasis {
                font-weight: 500;
            }

            .advantage-item {
                padding: var(--space-8);
            }

            .advantage-icon {
                width: 64px;
                height: 64px;
                font-size: 2rem;
            }

            .advantage-item h3 {
                font-size: var(--text-lg);
            }

            .advantage-emphasis {
                font-weight: 600;
            }

            .team-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
                overflow-x: auto;
                scroll-snap-type: x mandatory;
                display: flex;
                padding: 0 var(--space-4) var(--space-4) 0;
            }

            .team-card {
                min-width: 280px;
                flex-shrink: 0;
                padding: var(--space-8);
            }

            .team-avatar {
                width: 64px;
                height: 64px;
                font-size: var(--text-xl);
            }

            .team-card h3 {
                font-size: var(--text-lg);
            }

            .team-link {
                padding: var(--space-2) var(--space-4);
                font-size: var(--text-xs);
            }

            section {
                padding: var(--space-20) 0;
            }

            .accordion-button {
                padding: var(--space-4);
                font-size: var(--text-base);
            }

            .accordion-content-inner {
                padding: 0 var(--space-4) var(--space-4) var(--space-4);
            }

            .container {
                padding: 0 var(--space-4);
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: var(--text-4xl);
            }
            h2 {
                font-size: var(--text-3xl);
            }

            .hero h1 {
                font-size: var(--text-4xl);
            }

            .hero-tagline {
                font-size: var(--text-xl);
            }

            .hero-subtitle {
                font-size: var(--text-lg);
            }

            .hero-description {
                font-size: var(--text-base);
            }

            .cta-button {
                padding: var(--space-4) var(--space-8);
                font-size: var(--text-base);
            }

            .hero-logo {
                max-width: 180px;
            }

            .program-section {
                padding: var(--space-16) 0;
                margin: var(--space-10) 0;
            }

            .program-section h2 {
                font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
                margin-bottom: var(--space-6);
            }

            .program-section h2::after {
                width: 80px;
                height: 2px;
                bottom: -12px;
            }

            .program-description {
                font-size: clamp(var(--text-sm), 4vw, var(--text-base));
            }

            .program-description::before,
            .program-description::after {
                font-size: 1.5rem;
            }

            .program-description::before {
                top: -15px;
                left: -25px;
            }

            .program-description::after {
                bottom: -15px;
                right: -25px;
            }

            .program-highlight {
                padding: 1px 4px;
                font-size: var(--text-xs);
            }

            .team-section h2 {
                font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
                margin-bottom: var(--space-5);
            }

            .team-section h2::after {
                width: 60px;
                height: 2px;
                bottom: -10px;
            }

            .team-intro {
                font-size: clamp(var(--text-sm), 4vw, var(--text-base));
            }

            .team-intro::before,
            .team-intro::after {
                font-size: 1.5rem;
            }

            .team-intro::before {
                top: -15px;
                left: -25px;
            }

            .team-intro::after {
                bottom: -15px;
                right: -25px;
            }

            .future-section h2 {
                font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
                margin-bottom: var(--space-5);
            }

            .future-section h2::after {
                width: 80px;
                height: 2px;
                bottom: -10px;
            }

            .future-intro {
                font-size: clamp(var(--text-sm), 4vw, var(--text-base));
            }

            .future-intro::before,
            .future-intro::after {
                font-size: 1.5rem;
            }

            .future-intro::before {
                top: -15px;
                left: -25px;
            }

            .future-intro::after {
                bottom: -15px;
                right: -25px;
            }

            .future-card {
                padding: var(--space-8);
            }

            .future-card h3 {
                font-size: var(--text-xl);
                margin-bottom: var(--space-4);
            }

            .future-grid li {
                padding-left: var(--space-6);
                margin-bottom: var(--space-2);
            }

            .future-grid li::before {
                width: 20px;
                height: 20px;
                font-size: var(--text-xs);
            }

            .value-card {
                padding: var(--space-8);
            }

            .value-icon {
                width: 56px;
                height: 56px;
                margin-bottom: var(--space-4);
            }

            .value-icon i {
                width: 24px;
                height: 24px;
            }

            .value-card h3 {
                font-size: var(--text-base);
                margin-bottom: var(--space-3);
            }

            .advantage-item {
                padding: var(--space-6);
            }

            .advantage-icon {
                width: 56px;
                height: 56px;
                font-size: 1.5rem;
            }

            .advantage-item h3 {
                font-size: var(--text-base);
                margin-bottom: var(--space-3);
            }

            .advantage-emphasis {
                font-weight: 600;
            }

            .team-card {
                min-width: 260px;
                padding: var(--space-6);
            }

            .team-avatar {
                width: 56px;
                height: 56px;
                font-size: var(--text-lg);
            }

            .team-card h3 {
                font-size: var(--text-base);
            }

            .team-link {
                padding: var(--space-2) var(--space-3);
                font-size: var(--text-xs);
            }

            .accordion-button {
                padding: var(--space-4);
                font-size: var(--text-base);
            }

            .accordion-content-inner {
                padding: 0 var(--space-4) var(--space-4) var(--space-4);
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .cta-button:hover {
                transform: none;
                box-shadow: var(--shadow-md);
            }

            .value-card:hover,
            .advantage-item:hover {
                transform: none;
                box-shadow: var(--shadow-md);
            }

            .team-card:hover {
                transform: none;
                box-shadow: var(--shadow-md);
            }
        }

        /* Paper Texture Effect for All Sections */
        section {
            position: relative;
        }

        section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0, 0, 0, 0.01) 2px,
            rgba(0, 0, 0, 0.01) 4px
        );
    z-index: 0;
    opacity: 0.4;
    pointer-events: none;
    mix-blend-mode: multiply;
}

        /* Enhanced Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            padding: var(--space-20) 0;
            background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%);
            animation: heroGradient 8s ease-in-out infinite;
        }

        .hero::before {
            opacity: 0.3;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 20% 20%, rgba(220, 38, 38, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Vision Section paper texture enhancement */
        .vision-section::before {
            opacity: 0.4;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.04) 0%, transparent 60%),
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Program Section paper texture enhancement */
        .program-section::before {
            opacity: 0.3;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.03) 0%, transparent 60%),
                repeating-linear-gradient(
                    -45deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Team Section paper texture enhancement */
        #team::before {
            opacity: 0.35;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 25% 75%, rgba(16, 185, 129, 0.02) 0%, transparent 60%),
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Values Section paper texture enhancement */
        #values::before {
            opacity: 0.4;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 30% 80%, rgba(220, 38, 38, 0.02) 0%, transparent 60%),
                repeating-linear-gradient(
                    135deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Advantage Section paper texture enhancement */
        #advantage::before {
            opacity: 0.35;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 10% 20%, rgba(220, 38, 38, 0.02) 0%, transparent 50%),
                repeating-linear-gradient(
                    180deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Future Section paper texture enhancement */
        .future-section::before {
            opacity: 0.4;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 15% 85%, rgba(245, 158, 11, 0.02) 0%, transparent 60%),
                repeating-linear-gradient(
                    225deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Logistics Section paper texture enhancement */
        #logistics::before {
            opacity: 0.35;
            background: 
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
                radial-gradient(circle at 85% 15%, rgba(59, 130, 246, 0.02) 0%, transparent 60%),
                repeating-linear-gradient(
                    270deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 0, 0, 0.01) 2px,
                    rgba(0, 0, 0, 0.01) 4px
                );
        }

        /* Scholarship Section - Keep original styling but enhance it slightly */
        .scholarship::before {
            opacity: 0.5;
        }

        /* Ensure content containers are above paper textures */
        .hero-content,
        .vision-content, 
        .program-content,
        .team-intro-container,
        .advantage-intro,
        .future-intro-container,
        .values-intro,
        .container {
            position: relative;
            z-index: 2;
        }

        /* Fix for scholarship section */
        .scholarship-content {
            position: relative;
            z-index: 2;
        }
    </style>
    <style>
        /* Minimalist Hero Overrides with UC Brand Colors */
        .hero {
            padding: clamp(5rem, 12vh, 10rem) 0;
            background: var(--bg-color);
            animation: none;
        }
        .hero::before { display: none; }
        .parallax-bg { opacity: 0.18; }
        .network-bg { opacity: 0.02; }

        .hero-logo {
            max-width: 180px;
            margin-bottom: var(--space-6);
            filter: none;
        }
        .hero h1 {
            font-size: clamp(2rem, 5vw, 3.25rem);
            letter-spacing: -0.02em;
            margin-bottom: var(--space-4);
        }
        .hero-tagline {
            color: var(--text-secondary);
            margin: var(--space-3) 0;
            text-shadow: none;
        }
        .hero-subtitle {
            color: var(--text-tertiary);
            margin-top: var(--space-2);
        }
        .hero-description {
            max-width: 46ch;
            margin-top: var(--space-6);
            color: var(--text-secondary);
        }
        .hero-cta { margin-top: var(--space-8); }

        /* Eyebrow badge with UC colors */
        .eyebrow-badge {
            display: inline-flex;
            align-items: center;
            gap: .5rem;
            padding: .375rem .75rem;
            border-radius: 999px;
            background: var(--primary-lighter);
            color: var(--primary-dark);
            font-weight: 600;
            font-size: var(--text-sm);
            border: 1px solid var(--primary-light);
        }
        .eyebrow-dot {
            width: 8px; height: 8px; border-radius: 50%;
            background: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(200, 47, 72, 0.12);
        }

        @media (max-width: 768px){
            .hero-logo{ max-width: 140px; }
            .hero-description{ display: none; }
        }
    </style>
</head>
<body>
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Sticky Header -->
    <header class="sticky-header" id="stickyHeader">
        <div class="container">
            <img src="UC%20Logo.svg" alt="Ullens College" class="logo">
            <nav>
                <ul class="nav-links-desktop">
                    <li><a href="#vision">Vision</a></li>
                    <li><a href="#values">Values</a></li>
                    <li><a href="#briefing">Program</a></li>
                    <li><a href="#advantage">Advantage</a></li>
                    <li><a href="#future">Future</a></li>
                    <li><a href="#team">Team</a></li>
                    <li><a href="#logistics">FAQs</a></li>
                </ul>
            </nav>
            <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button" target="_blank" rel="noopener noreferrer" style="padding: var(--space-2) var(--space-4); font-size: var(--text-sm); min-height: auto;">
                <span>Apply Now</span>
            </a>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav class="mobile-nav">
        <img src="UC%20Logo.svg" alt="Ullens College" style="height: 40px;">
        <button class="nav-toggle" id="navToggle">☰</button>
    </nav>

    <!-- Navigation Menu -->
    <div class="nav-overlay" id="navOverlay"></div>
    <div class="nav-menu" id="navMenu">
        <button class="nav-toggle" id="navClose" style="margin-left: auto; display: block;">✕</button>
        <ul class="nav-links">
            <li><a href="#vision">Vision</a></li>
            <li><a href="#values">Values</a></li>
            <li><a href="#briefing">Program</a></li>
            <li><a href="#advantage">Advantage</a></li>
            <li><a href="#future">Future</a></li>
            <li><a href="#team">Team</a></li>
            <li><a href="#logistics">FAQs</a></li>
            <li><a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" target="_blank" rel="noopener noreferrer" class="cta-button" style="margin-top: 1rem; text-align: center;"><span>Apply Now</span></a></li>
        </ul>
    </div>

    <!-- Section 1: Hero -->
    <main id="main-content">
    <section class="hero" id="home" role="region" aria-label="Hero">
        <div class="parallax-bg"></div>
        <div class="network-bg" id="networkBg"></div>
        <div class="hero-content">
            <div class="eyebrow-badge fade-in" aria-label="Admissions 2025">
                <span class="eyebrow-dot" aria-hidden="true"></span>
                <span>Admissions 2025 • Limited seats</span>
            </div>
            <img src="UC%20Logo.svg" alt="Ullens College Logo" class="hero-logo fade-in">
            <h1 class="fade-in">B.Tech in Cybersecurity</h1>
            <h3 class="hero-tagline fade-in">Where innovation meets integrity</h3>
            <h3 class="hero-subtitle fade-in">In partnership with Kathmandu University</h3>
            <p class="hero-description fade-in">A world‑class 4‑year program blending theory and hands‑on labs, guided by industry mentors—right here in Khumaltar.</p>
            <div class="hero-cta">
                <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button ripple fade-in" target="_blank" rel="noopener noreferrer">
                    <span>APPLY NOW</span>
                </a>
                <p class="fade-in" style="color: var(--text-muted); font-size: var(--text-sm); margin-top: var(--space-3);">Takes ~2 minutes on Google Forms</p>
            </div>
        </div>

    </section>

    <div class="container">
        <!-- Vision Section -->
        <section id="vision" class="vision-section fade-in">
            <div class="vision-content">
                <h2>Vision</h2>
                <p class="vision-statement">
                    Ullens College aims to <span class="vision-emphasis">set standards of excellence</span> by nurturing <span class="vision-emphasis">technologically advanced and ethically grounded graduates</span> who embrace <span class="vision-emphasis">interdisciplinary learning</span>, <span class="vision-emphasis">think deeply</span>, <span class="vision-emphasis">solve problems creatively</span>, and act with a <span class="vision-emphasis">global mindset</span> in a rapidly changing world.
                </p>
            </div>
        </section>

        <!-- Values Section -->
        <section id="values" class="fade-in">
            <div class="values-section">
                <div class="container">
                    <div class="values-intro">
                        <h2>Our Core Values</h2>
                        <p class="values-subtitle">
                            The principles that guide our mission to create ethical, innovative, and globally-minded cybersecurity leaders.
                        </p>
                    </div>
                    
                    <div class="values-grid">
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="target"></i>
                            </div>
                            <div class="value-content">
                                <h3>Excellence and Deep Inquiry</h3>
                                <p>We pursue the <span class="value-emphasis">highest standards</span> in teaching, research, and personal growth. We believe that true excellence is rooted in <span class="value-emphasis">curiosity, rigorous thinking</span>, and a relentless drive to <span class="value-emphasis">understand the world more deeply</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="palette"></i>
                            </div>
                            <div class="value-content">
                                <h3>Creativity and Innovation</h3>
                                <p>We thrive at the <span class="value-emphasis">intersections</span>—where disciplines meet, ideas collide, and new solutions emerge. We foster <span class="value-emphasis">creative problem-solving</span> and empower our community to <span class="value-emphasis">cross boundaries and innovate</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="scale"></i>
                            </div>
                            <div class="value-content">
                                <h3>Integrity and Ethics</h3>
                                <p>We hold ourselves to the highest standards of <span class="value-emphasis">honesty, transparency, and ethical conduct</span>. Technological advancement must be guided by a <span class="value-emphasis">strong moral compass</span> and <span class="value-emphasis">serve the greater good</span>.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="globe"></i>
                            </div>
                            <div class="value-content">
                                <h3>Global Mindset</h3>
                                <p>We prepare graduates to <span class="value-emphasis">thrive in a rapidly changing, interconnected world</span>. We cultivate <span class="value-emphasis">cross-cultural understanding</span>, <span class="value-emphasis">global awareness</span>, and adaptability to new environments and ideas.</p>
                            </div>
                        </div>
                        
                        <div class="value-card fade-in">
                            <div class="value-icon">
                                <i data-lucide="users"></i>
                            </div>
                            <div class="value-content">
                                <h3>Community and Belonging</h3>
                                <p>We build a <span class="value-emphasis">welcoming, inclusive, and supportive community</span> where everyone can flourish. We <span class="value-emphasis">value diversity in all its forms</span> and believe <span class="value-emphasis">every voice enriches our collective journey</span>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 2: The Program -->
        <section id="briefing" class="program-section fade-in">
            <div class="program-content">
                <h2>The Program: B.Tech. in Cybersecurity</h2>
                <p class="program-description">
                    Think of this as your <span class="program-emphasis">four-year launchpad</span> to becoming an <span class="program-emphasis">expert digital defender</span>. It's a <span class="program-emphasis">full-time, hands-on program</span>, affiliated with Kathmandu University, where you'll learn to <span class="program-emphasis">design, defend, and dominate</span> in the world of digital security. We don't just teach you to use the tools; <span class="program-highlight">we teach you to build them.</span>
                </p>

                <div style="text-align: center; margin-top: var(--space-8);">
                    <a href="https://www.canva.com/design/DAGvc8alXlc/6s6xtwmz0lsYmylgTFneEA/view?utm_content=DAGvc8alXlc&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=hc56c1e4eaa"
                       target="_blank"
                       rel="noopener noreferrer"
                       class="cta-button"
                       style="background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple)); display: inline-flex; align-items: center; gap: 0.5rem; padding: var(--space-3) var(--space-6); font-size: var(--text-base);">
                        <i data-lucide="book-open" style="width: 20px; height: 20px;"></i>
                        <span>View Curriculum</span>
                    </a>
                </div>
            </div>
        </section>

        <!-- Section 7: Team -->
        <section id="team" class="fade-in">
            <div class="team-section">
                <div class="container">
                    <div class="team-intro-container">
                        <h2>Our Team</h2>
                        <p class="team-intro">
                            Our program is led by a <span class="team-emphasis">distinguished team of passionate educators</span>, <span class="team-emphasis">industry veterans</span>, and thought leaders with extensive backgrounds in cybersecurity, technology, and academia. They bring together <span class="team-emphasis">cutting-edge research</span>, <span class="team-emphasis">real-world experience</span>, and <span class="team-emphasis">innovative teaching methods</span> to guide the next generation of cybersecurity experts.
                        </p>
                    </div>
                    <div class="team-grid">
                        <div class="team-card fade-in">
                            <div class="team-avatar">SA</div>
                            <h3>Shishir Adhikari, PhD</h3>
                            <p class="team-role">Academic Director</p>
                            <a href="https://www.linkedin.com/in/arshishir/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Shishir Adhikari LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">TG</div>
                            <h3>Topraj Gurung, PhD</h3>
                            <p class="team-role">Industry Faculty</p>
                            <a href="https://www.linkedin.com/in/topraj/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Topraj Gurung LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">MA</div>
                            <h3>Monil Adhikari</h3>
                            <p class="team-role">Founding Faculty</p>
                            <a href="https://www.linkedin.com/in/moniladhikari/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Monil Adhikari LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">HK</div>
                            <h3>Himalaya Kakshapati</h3>
                            <p class="team-role">Founding Faculty</p>
                            <a href="https://www.linkedin.com/in/himalaya-kakshapati/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Himalaya Kakshapati LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">SD</div>
                            <h3>Saroj Dhakal</h3>
                            <p class="team-role">Director of Technology</p>
                            <a href="https://www.linkedin.com/in/sarojdhakal" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Saroj Dhakal LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">MS</div>
                            <h3>Manish Sharma</h3>
                            <p class="team-role">Industry Faculty</p>
                            <a href="https://www.linkedin.com/in/manishksharma01/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Manish Sharma LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                        <div class="team-card fade-in">
                            <div class="team-avatar">SB</div>
                            <h3>Sulav Bhatta</h3>
                            <p class="team-role">Head of Career Counseling</p>
                            <a href="https://www.linkedin.com/in/sulav-raj-bhatta-a01718186/" target="_blank" rel="noopener noreferrer" class="team-link" aria-label="Sulav Bhatta LinkedIn Profile">
                                <i data-lucide="linkedin"></i>
                                Connect on LinkedIn
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 3: Why We're Different -->
        <section id="advantage" class="fade-in">
            <div class="advantage-section">
                <div class="container">
                    <div class="advantage-intro">
                        <h2>The Ullens Advantage</h2>
                        <p style="text-align: center; max-width: 900px; margin: 0 auto; font-size: var(--text-lg); line-height: var(--leading-relaxed); color: var(--text-secondary);">
                            We built this program differently from the ground up. Our revolutionary approach combines engineering excellence, security mastery, future-ready AI integration, and leadership development into one transformative educational experience.
                        </p>
                    </div>
                    <div class="advantage-grid">
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="building-2"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Architect's Blueprint</h3>
                                <p>The <span class="advantage-emphasis">backbone of our course is cutting-edge computer engineering</span>. You first learn how to build complex digital systems from scratch—from hardware fundamentals to software architecture. You can't protect a fortress until you understand every stone in its foundation.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="shield-check"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Fortress Walls</h3>
                                <p>We then <span class="advantage-emphasis">fortify that engineering core with layers of advanced cybersecurity</span>. You'll master ethical hacking, digital forensics, threat intelligence, and incident response to defend the systems you now understand at the deepest level.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="brain-circuit"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Futurist's Toolkit</h3>
                                <p>The <span class="advantage-emphasis">future is AI-driven</span>, and so are its threats. We've <span class="advantage-emphasis">integrated AI courses</span> throughout the curriculum—from machine learning security to AI-powered threat detection—ensuring you're prepared for tomorrow's cyber landscape.</p>
                            </div>
                        </div>
                        <div class="advantage-item fade-in">
                            <div class="advantage-icon">
                                <i data-lucide="lightbulb"></i>
                            </div>
                            <div class="advantage-content">
                                <h3>The Leader's Mindset</h3>
                                <p>Our <span class="advantage-emphasis">guiding philosophy is inspired by the liberal arts</span>—we <span class="advantage-emphasis">focus on how to think, not just what to think</span>. Through <span class="advantage-emphasis">project-based learning, critical thinking, communication, and problem-solving skills</span>, you'll become <span class="advantage-emphasis">a leader, not just a technician</span>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 4: Your Future - Careers & Global Opportunities -->
        <section id="future" class="future-section fade-in">
            <div class="future-intro-container">
                <h2>Your Future Awaits</h2>
                <p class="future-intro">
                    Step into a world of <span class="future-emphasis">limitless possibilities</span> where your cybersecurity expertise opens doors to <span class="future-emphasis">high-impact careers</span> and <span class="future-emphasis">global opportunities</span>. Your journey doesn't end with graduation—it's just the beginning.
                </p>
            </div>
            <div class="future-grid">
                <div class="future-card fade-in">
                    <h3>Career Prospects</h3>
                    <p><strong>What kind of job can I get?</strong></p>
                    <p>Cybersecurity is one of the <span class="future-emphasis">fastest-growing and highest-paid fields globally</span>. Your future career battlegrounds could include:</p>
                    <ul>
                        <li>Penetration Tester (Ethical Hacker)</li>
                        <li>Digital Forensics Investigator</li>
                        <li>Security Architect</li>
                        <li>Chief Information Security Officer (CISO)</li>
                        <li>Security Consultant</li>
                        <li>Software Engineer</li>
                    </ul>
                </div>
                <div class="future-card fade-in">
                    <h3>Global Master's Pathways</h3>
                    <p><strong>Can I use this degree to study abroad?</strong></p>
                    <p><span class="future-emphasis">Absolutely!</span> This degree is your <span class="future-emphasis">ticket to advanced studies worldwide</span>. Plus, our sister organization, <a href="https://ullensleap.org/" target="_blank" rel="noopener noreferrer">LEAP</a>, provides expert counseling to help you target and apply to the <span class="future-emphasis">best institutions</span> in the US, UK, Australia, and beyond.</p>
                </div>
            </div>
        </section>

        <!-- Section 6: The Logistics -->
        <section id="logistics" class="fade-in">
            <h2>Frequently Asked Questions</h2>
            <div class="accordion">
                <div class="accordion-item">
                    <button class="accordion-button">Who can apply? Am I eligible?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p>If you're passionate about tech and have a background in Mathematics and Computer Science (or an equivalent course) from any board (+2, A-Levels, etc.), you are welcome to apply. We're looking for curious minds from all streams, including Science and Management.</p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-button">What's the entrance exam like?</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p>The entrance exam is a 2-hour Paper-Based Test (PBT) that explores your skills in English, Mathematics, and Computer Science.</p>
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <button class="accordion-button">What's the investment? (Program Cost)</button>
                    <div class="accordion-content">
                        <div class="accordion-content-inner">
                            <p>For the most up-to-date fee structure, it's best to contact our admissions office directly. They'll give you all the details with zero pressure.</p>
                        </div>
                    </div>
                </div>

            </div>
        </section>

        <!-- Section 7: Scholarship -->
        <section class="scholarship fade-in">
            <div class="scholarship-content">
                <i data-lucide="heart-handshake" class="scholarship-icon"></i>
                
                <h2>Talent is Universal. Opportunity Should Be Too.</h2>
                
                <p class="scholarship-subtitle">
                    Breaking barriers to world-class cybersecurity education
                </p>
                
                <p class="scholarship-description">
                    We believe your potential—not your financial situation—should define your future. Ullens College is committed to ensuring that the brightest minds from all backgrounds can access world-class cybersecurity education and join our mission to safeguard the digital world.
                </p>

                <div class="scholarship-highlight">
                    <div class="highlight-title">Need-Blind Scholarship Program</div>
                    <p class="highlight-text">
                        Our admissions process evaluates talent, passion, and potential—never your ability to pay. Financial need will never be a barrier to exceptional education.
                    </p>
                </div>
                
                <div class="scholarship-highlight" style="background-color: rgba(200, 47, 72, 0.15); margin-top: 12px;">
                    <div class="highlight-title">Limited Enrollment Opportunity</div>
                    <p class="highlight-text">
                        The B.Tech in Cybersecurity program has limited seats to ensure personalized attention and optimal learning outcomes. Early applications are strongly encouraged.
                    </p>
                </div>

                <div class="scholarship-features">
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="graduation-cap" class="feature-icon"></i>
                        <h3>Merit-Based Selection</h3>
                        <p>Scholarships awarded purely on academic excellence, potential, and passion for cybersecurity—regardless of financial background.</p>
                    </div>
                    
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="users" class="feature-icon"></i>
                        <h3>Diverse Community</h3>
                        <p>Building a vibrant learning environment where students from all socioeconomic backgrounds learn and grow together.</p>
                    </div>
                    
                    <div class="scholarship-feature fade-in">
                        <i data-lucide="globe" class="feature-icon"></i>
                        <h3>Global Impact</h3>
                        <p>Empowering talented individuals to become cybersecurity leaders who will protect our digital future, regardless of their origin.</p>
                    </div>
                </div>

                <div class="scholarship-cta">
                    <a href="#footer" class="cta-button">
                        <span>Contact Us</span>
                    </a>
                </div>
            </div>
        </section>


    </div>

    <footer id="footer">
        <div class="footer-content">
            <div class="footer-main">
                <img src="UC%20Logo.svg" alt="Ullens College" class="footer-logo">
                <h3>Get in Touch</h3>
                <p class="footer-description">
                    Connect with us to learn more about our innovative cybersecurity program at Ullens College.
                </p>
                
                <div class="footer-contact">
                    <div class="contact-item">
                        <i data-lucide="map-pin" class="contact-icon"></i>
                        <p>Khumaltar, Lalitpur-15, Nepal</p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="phone" class="contact-icon"></i>
                        <p><a href="tel:+977-1-5411137">977-1-5411137</a></p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="mail" class="contact-icon"></i>
                        <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                    <div class="contact-item">
                        <i data-lucide="clock" class="contact-icon"></i>
                        <p>Mon - Fri: 9:00 AM - 5:00 PM</p>
                    </div>
                </div>
                
                <div class="footer-social">
                    <a href="https://www.facebook.com/profile.php?id=61577290536656" class="social-link" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                        <i data-lucide="facebook"></i>
                    </a>
                    <a href="https://www.linkedin.com/company/ullens-college" class="social-link" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                        <i data-lucide="linkedin"></i>
                    </a>
                    <a href="https://ullenscollege.vercel.app/" class="social-link" target="_blank" rel="noopener noreferrer" style="background: linear-gradient(135deg, #3B82F6, #10B981);" aria-label="Preview our future site">
                        <i data-lucide="sparkles"></i>
                    </a>
                </div>
                
                <div style="margin-top: 15px; font-size: var(--text-sm); background: linear-gradient(90deg, #3B82F6, #10B981); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; display: inline-block; border-bottom: 1px dashed #CBD5E1; padding-bottom: 2px; cursor: pointer;" class="easter-egg">
                    <i data-lucide="wand-sparkles" style="width: 14px; height: 14px; vertical-align: middle; margin-right: 4px; stroke: #3B82F6;"></i>
                    <span>Psst! Want to see something magical?</span>
                </div>
                
                <script>
                    document.querySelector('.easter-egg').addEventListener('click', function() {
                        window.open('https://ullenscollege.vercel.app/', '_blank');
                    });
                </script>
            </div>
            
            <div class="footer-cta">
                <i data-lucide="rocket" style="width: 40px; height: 40px; color: var(--primary-color); margin-bottom: var(--space-3);"></i>
                <h4>Ready to Launch Your Future?</h4>
                <p>Join the pioneering class of cybersecurity experts at Ullens College.</p>
                <a href="https://forms.gle/JqHLLBuxyW3Ytsvq6" class="cta-button ripple" target="_blank" rel="noopener noreferrer" style="margin: 0;">
                    <span>APPLY NOW</span>
                </a>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2025 Ullens College. All rights reserved. | Designed with love for UEF <span class="designer-credit" style="cursor: help; position: relative; border-bottom: 1px dotted rgba(200, 47, 72, 0.4);">♥</span></p>
            
            <script>
                document.querySelector('.designer-credit').addEventListener('click', function() {
                    // Create toast element
                    const toast = document.createElement('div');
                    toast.style.position = 'fixed';
                    toast.style.bottom = '30px';
                    toast.style.left = '50%';
                    toast.style.transform = 'translateX(-50%)';
                    toast.style.background = 'rgba(255, 255, 255, 0.95)';
                    toast.style.backdropFilter = 'blur(10px)';
                    toast.style.padding = '10px 20px';
                    toast.style.borderRadius = '8px';
                    toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                    toast.style.fontWeight = '500';
                    toast.style.fontSize = '14px';
                    toast.style.color = 'var(--primary-darker)';
                    toast.style.border = '1px solid rgba(200, 47, 72, 0.2)';
                    toast.style.zIndex = '1000';
                    toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateX(-50%) translateY(20px)';
                    toast.innerHTML = 'Crafted with ♥ by <strong>Shishir</strong>';
                    
                    // Add to DOM
                    document.body.appendChild(toast);
                    
                    // Trigger animation
                    setTimeout(() => {
                        toast.style.opacity = '1';
                        toast.style.transform = 'translateX(-50%) translateY(0)';
                    }, 10);
                    
                    // Remove after delay
                    setTimeout(() => {
                        toast.style.opacity = '0';
                        toast.style.transform = 'translateX(-50%) translateY(20px)';
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 300);
                    }, 3000);
                });
            </script>
        </div>
    </footer>
    </main>

    <script>
        // Modern App Architecture
        class UllensCollegeApp {
            constructor() {
                this.components = new Map();
                this.initializeApp();
            }

            async initializeApp() {
                // Performance optimization
                this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
                
                // Initialize all components
                this.components.set('accordion', new AccordionComponent());
                this.components.set('navigation', new NavigationComponent());
                this.components.set('scrollEffects', new ScrollEffectsComponent());
                this.components.set('animations', new AnimationsComponent());
                
                // Run an initial UI update so header/progress reflect current scroll position
                this.components.get('scrollEffects').update();

                // Setup global event listeners
                this.setupEventListeners();
                this.setupIntersectionObserver();
                
                // Register service worker for PWA
                this.registerServiceWorker();
                
                console.log('✅ Ullens College App initialized');
            }

            setupEventListeners() {
                // Modern event delegation
                document.addEventListener('click', this.handleGlobalClick.bind(this));
                window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));
                window.addEventListener('resize', this.debounce(this.handleResize.bind(this), 250));
                
                // Modern keyboard navigation
                document.addEventListener('keydown', this.handleKeyboard.bind(this));
            }

            handleGlobalClick(event) {
                const target = event.target.closest('[data-action]');
                if (!target) return;

                const action = target.dataset.action;
                const component = target.dataset.component;

                if (component && this.components.has(component)) {
                    this.components.get(component).handleAction(action, target, event);
                }
            }

            handleScroll() {
                this.components.get('scrollEffects').update();
            }

            handleResize() {
                this.components.forEach(component => {
                    if (component.handleResize) component.handleResize();
                });
            }

            handleKeyboard(event) {
                // ESC key closes modals/menus
                if (event.key === 'Escape') {
                    this.components.get('navigation').close();
                }

                // Accordion keyboard support: Up/Down/Home/End
                const focused = document.activeElement;
                if (focused && focused.classList && focused.classList.contains('accordion-button')) {
                    const buttons = Array.from(document.querySelectorAll('.accordion-button'));
                    const idx = buttons.indexOf(focused);
                    if (event.key === 'ArrowDown') {
                        event.preventDefault();
                        buttons[Math.min(idx + 1, buttons.length - 1)]?.focus();
                    } else if (event.key === 'ArrowUp') {
                        event.preventDefault();
                        buttons[Math.max(idx - 1, 0)]?.focus();
                    } else if (event.key === 'Home') {
                        event.preventDefault();
                        buttons[0]?.focus();
                    } else if (event.key === 'End') {
                        event.preventDefault();
                        buttons[buttons.length - 1]?.focus();
                    }
                }
            }

            setupIntersectionObserver() {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -10% 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('visible');
                            // Unobserve for performance
                            observer.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                document.querySelectorAll('.fade-in').forEach(el => observer.observe(el));
            }

            async registerServiceWorker() {
                if ('serviceWorker' in navigator) {
                    try {
                        // Inline service worker for minimal setup
                        const swCode = `
                            self.addEventListener('install', () => self.skipWaiting());
                            self.addEventListener('activate', () => self.clients.claim());
                            self.addEventListener('fetch', event => {
                                if (event.request.url.includes('.css') || event.request.url.includes('.js')) {
                                    event.respondWith(
                                        caches.open('ullens-v1').then(cache => 
                                            cache.match(event.request) || fetch(event.request)
                                        )
                                    );
                                }
                            });
                        `;
                        
                        const blob = new Blob([swCode], { type: 'application/javascript' });
                        const registration = await navigator.serviceWorker.register(URL.createObjectURL(blob));
                        console.log('✅ Service Worker registered');
                    } catch (error) {
                        console.log('ℹ️ Service Worker registration failed:', error);
                    }
                }
            }

            // Utility functions
            throttle(func, limit) {
                let inThrottle;
                return function() {
                    if (!inThrottle) {
                        func.apply(this, arguments);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                }
            }

            debounce(func, wait) {
                let timeout;
                return function(...args) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => func.apply(this, args), wait);
                };
            }
        }

        // Accordion Component
        class AccordionComponent {
            constructor() {
                this.items = document.querySelectorAll('.accordion-item');
                this.init();
            }

            init() {
                this.items.forEach((item, index) => {
                    const button = item.querySelector('.accordion-button');
                    const content = item.querySelector('.accordion-content');
                    const contentId = `accordion-content-${index+1}`;
                    button.dataset.action = 'toggle';
                    button.dataset.component = 'accordion';
                    button.setAttribute('aria-expanded', 'false');
                    button.setAttribute('aria-controls', contentId);
                    button.setAttribute('role', 'button');
                    content.id = contentId;
                    content.setAttribute('role', 'region');
                    content.setAttribute('aria-labelledby', contentId);
                });
            }

            handleAction(action, target, event) {
                if (action === 'toggle') {
                    this.toggle(target.closest('.accordion-item'));
                }
            }

            toggle(item) {
                const content = item.querySelector('.accordion-content');
                const isActive = item.classList.contains('active');

                // Close other items
                this.items.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        this.close(otherItem);
                    }
                });

                // Toggle current item
                if (isActive) {
                    this.close(item);
                } else {
                    this.open(item);
                }
            }

            open(item) {
                const content = item.querySelector('.accordion-content');
                const button = item.querySelector('.accordion-button');
                item.classList.add('active');
                content.style.maxHeight = content.scrollHeight + 'px';
                button.setAttribute('aria-expanded', 'true');
            }

            close(item) {
                const content = item.querySelector('.accordion-content');
                const button = item.querySelector('.accordion-button');
                item.classList.remove('active');
                content.style.maxHeight = '0';
                button.setAttribute('aria-expanded', 'false');
            }
        }

        // Navigation Component
        class NavigationComponent {
            constructor() {
                this.navToggle = document.getElementById('navToggle');
                this.navClose = document.getElementById('navClose');
                this.navMenu = document.getElementById('navMenu');
                this.navOverlay = document.getElementById('navOverlay');
                this.isOpen = false;
                this.init();
            }

            init() {
                // Set up data attributes for modern event handling
                if (this.navToggle) {
                    this.navToggle.dataset.action = 'open';
                    this.navToggle.dataset.component = 'navigation';
                }
                if (this.navClose) {
                    this.navClose.dataset.action = 'close';
                    this.navClose.dataset.component = 'navigation';
                }
                if (this.navOverlay) {
                    this.navOverlay.dataset.action = 'close';
                    this.navOverlay.dataset.component = 'navigation';
                }

                // Set up nav links
                document.querySelectorAll('.nav-links a, .nav-links-desktop a').forEach(link => {
                    link.dataset.action = 'close';
                    link.dataset.component = 'navigation';
                });

                // Accessibility: capture first focusable element in menu
                if (this.navMenu) {
                    this.firstFocusable = this.navMenu.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
                }
            }

            handleAction(action, target, event) {
                switch (action) {
                    case 'open':
                        this.open();
                        break;
                    case 'close':
                        this.close();
                        break;
                    case 'toggle':
                        this.toggle();
                        break;
                }
            }

            toggle() {
                this.isOpen ? this.close() : this.open();
            }

            open() {
                if (this.navMenu && this.navOverlay) {
                    this.navMenu.classList.add('active');
                    this.navOverlay.classList.add('active');
                    document.body.style.overflow = 'hidden';
                    this.isOpen = true;
                    // Move focus into the menu
                    setTimeout(() => this.firstFocusable?.focus(), 0);
                    // Trap focus inside menu
                    this.focusHandler = (e) => {
                        if (!this.isOpen) return;
                        const focusables = Array.from(this.navMenu.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])'));
                        const first = focusables[0];
                        const last = focusables[focusables.length - 1];
                        if (e.key === 'Tab') {
                            if (e.shiftKey && document.activeElement === first) {
                                e.preventDefault();
                                last.focus();
                            } else if (!e.shiftKey && document.activeElement === last) {
                                e.preventDefault();
                                first.focus();
                            }
                        }
                    };
                    document.addEventListener('keydown', this.focusHandler);
                }
            }

            close() {
                if (this.navMenu && this.navOverlay) {
                    this.navMenu.classList.remove('active');
                    this.navOverlay.classList.remove('active');
                    document.body.style.overflow = '';
                    this.isOpen = false;
                    // Return focus to toggle
                    this.navToggle?.focus();
                    if (this.focusHandler) {
                        document.removeEventListener('keydown', this.focusHandler);
                        this.focusHandler = null;
                    }
                }
            }
        }

        // Scroll Effects Component
        class ScrollEffectsComponent {
            constructor() {
                this.progressBar = document.getElementById('progressBar');
                this.stickyHeader = document.getElementById('stickyHeader');
                this.lastScrollY = 0;
                this.ticking = false;
            }

            update() {
                if (!this.ticking) {
                    requestAnimationFrame(() => {
                        this.updateProgressBar();
                        this.updateStickyHeader();
                        this.ticking = false;
                    });
                    this.ticking = true;
                }
            }

            updateProgressBar() {
                if (!this.progressBar) return;
                
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = Math.min((scrollTop / docHeight) * 100, 100);
                this.progressBar.style.width = `${scrollPercent}%`;
            }

            updateStickyHeader() {
                if (!this.stickyHeader) return;
                
                const scrollTop = window.pageYOffset;
                const threshold = 120;

                if (scrollTop > threshold) {
                    this.stickyHeader.classList.add('visible');
                } else {
                    this.stickyHeader.classList.remove('visible');
                }

                this.lastScrollY = scrollTop;
            }
        }

        // Animations Component
        class AnimationsComponent {
            constructor() {
                this.createNetworkAnimation();
                this.setupRippleEffects();
                this.setupTouchSwipe();
                this.setupSmoothScrolling();
                this.setupParallaxEffect();
                this.initializeHeroAnimations();
            }

            createNetworkAnimation() {
                const networkBg = document.getElementById('networkBg');
                if (!networkBg) return;

                networkBg.innerHTML = '';

                // Create floating nodes with better performance
                for (let i = 0; i < 8; i++) {
                    const node = document.createElement('div');
                    node.className = 'network-node';
                    node.style.left = `${Math.random() * 100}%`;
                    node.style.top = `${Math.random() * 100}%`;
                    node.style.animationDelay = `${Math.random() * 8}s`;
                    networkBg.appendChild(node);
                }

                // Create connecting lines
                for (let i = 0; i < 4; i++) {
                    const line = document.createElement('div');
                    line.className = 'network-line';
                    line.style.left = `${Math.random() * 100}%`;
                    line.style.top = `${Math.random() * 100}%`;
                    line.style.width = `${Math.random() * 100 + 30}px`;
                    line.style.transform = `rotate(${Math.random() * 360}deg)`;
                    line.style.animationDelay = `${Math.random() * 6}s`;
                    networkBg.appendChild(line);
                }
            }

            setupRippleEffects() {
                document.addEventListener('click', (e) => {
                    const button = e.target.closest('.ripple');
                    if (!button) return;

                    const ripple = document.createElement('span');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    Object.assign(ripple.style, {
                        width: `${size}px`,
                        height: `${size}px`,
                        left: `${x}px`,
                        top: `${y}px`
                    });
                    
                    ripple.classList.add('ripple-effect');
                    button.appendChild(ripple);

                    setTimeout(() => ripple.remove(), 600);
                });
            }

            setupTouchSwipe() {
                const swipeElements = document.querySelectorAll('.team-grid, .mentors-grid');
                
                swipeElements.forEach(element => {
                    let startX = 0;
                    let scrollLeft = 0;

                    element.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].pageX - element.offsetLeft;
                        scrollLeft = element.scrollLeft;
                    }, { passive: true });

                    element.addEventListener('touchmove', (e) => {
                        if (!startX) return;
                        const x = e.touches[0].pageX - element.offsetLeft;
                        const walk = (x - startX) * 2;
                        element.scrollLeft = scrollLeft - walk;
                    }, { passive: true });

                    element.addEventListener('touchend', () => {
                        startX = 0;
                    }, { passive: true });
                });
            }

            setupSmoothScrolling() {
                document.addEventListener('click', (e) => {
                    const anchor = e.target.closest('a[href^="#"]');
                    if (!anchor) return;

                    e.preventDefault();
                    const targetId = anchor.getAttribute('href');
                    const target = document.querySelector(targetId);
                    
                    if (target) {
                        // Use a smaller offset for footer to avoid weird animations
                        const headerOffset = targetId === '#footer' ? 20 : 80;
                        const elementPosition = target.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        // Use requestAnimationFrame for smoother animation
                        const startPosition = window.pageYOffset;
                        const distance = offsetPosition - startPosition;
                        const duration = Math.min(Math.abs(distance) / 2, 800); // Max 800ms
                        let start = null;

                        function animateScroll(timestamp) {
                            if (!start) start = timestamp;
                            const progress = timestamp - start;
                            const percentage = Math.min(progress / duration, 1);
                            
                            // Easing function for smoother animation
                            const easeOutCubic = 1 - Math.pow(1 - percentage, 3);
                            
                            window.scrollTo(0, startPosition + (distance * easeOutCubic));
                            
                            if (progress < duration) {
                                requestAnimationFrame(animateScroll);
                            }
                        }

                        requestAnimationFrame(animateScroll);
                    }
                });
            }

            setupParallaxEffect() {
                let ticking = false;
                
                const updateParallax = () => {
                    const scrolled = window.pageYOffset;
                    const parallaxElements = document.querySelectorAll('.parallax-bg');

                    parallaxElements.forEach(element => {
                        const speed = 0.3; // Reduced for better performance
                        element.style.transform = `translate3d(0, ${scrolled * speed}px, 0)`;
                    });
                    
                    ticking = false;
                };

                window.addEventListener('scroll', () => {
                    if (!ticking) {
                        requestAnimationFrame(updateParallax);
                        ticking = true;
                    }
                }, { passive: true });
            }

            initializeHeroAnimations() {
                // Stagger hero animations with better timing
                setTimeout(() => {
                    document.querySelectorAll('.hero .fade-in').forEach((el, index) => {
                        setTimeout(() => el.classList.add('visible'), index * 150);
                    });
                }, 300);
            }

            handleResize() {
                // Recreate network animation on resize for responsiveness
                this.createNetworkAnimation();
            }
        }

        // Initialize the app when DOM is loaded
        let app;

        const initializeApp = () => {
            app = new UllensCollegeApp();
            
            // Initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('✅ Lucide icons initialized');
            }
            
            console.log('🚀 Modern Ullens College website loaded');
        };

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }

        // Legacy function support for backward compatibility
        function openNav() { app?.components.get('navigation')?.open(); }
        function closeNav() { app?.components.get('navigation')?.close(); }
        function toggleNav() { app?.components.get('navigation')?.toggle(); }
    </script>

</body>
</html>
