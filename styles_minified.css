:root {
--primary-color: #c82f48;          /* UC Logo primary color */
--primary-dark: #a02639;           /* Darker shade */
--primary-darker: #7d1d2a;        /* Darkest shade */
--primary-lighter: #faf7f8;       /* Lightest tint */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #3B82F6;
--paper-bg: #FAFAF7;
--transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
--transition-medium: 300ms cubic-bezier(0.4, 0, 0.2, 1);
--transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
--space-24: 6rem;     /* 96px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
--text-5xl: 3rem;       /* 48px */
--text-6xl: 3.75rem;    /* 60px */
--text-7xl: 4.5rem;     /* 72px */
@supports (container-type: inline-size) {
.container {
container-type: inline-size;
container-name: main-container;
}
}
.scroll-container {
scroll-snap-type: y mandatory;
overflow-y: scroll;
height: 100vh; /* fallback */
height: 100dvh;
}
section {
scroll-snap-align: start;
}
body {
background-color: var(--paper-bg);
.loading-skeleton {
background: linear-gradient(90deg, 
var(--bg-secondary) 25%, 
var(--border-color) 50%, 
var(--bg-secondary) 75%);
background-size: 200% 100%;
animation: loading 1.5s infinite ease-in-out;
}
@keyframes loading {
0% { background-position: 200% 0; }
100% { background-position: -200% 0; }
}
.fade-in {
opacity: 0;
transform: translateY(20px);
transition: opacity var(--transition-slow), transform var(--transition-slow);
}
.fade-in.visible {
opacity: 1;
transform: translateY(0);
}
.container {
max-width: 1280px;
margin: 0 auto;
padding: 0 var(--space-6);
position: relative;
}
section {
padding: clamp(2.5rem, 6vw, 5rem) 0; /* responsive vertical rhythm */
position: relative;
}
.vision-emphasis {
font-weight: 500;
color: var(--text-color);
border-bottom: 1px solid transparent;
transition: all var(--transition-medium);
}
.vision-section:hover .vision-emphasis {
border-bottom-color: rgba(220, 38, 38, 0.2);
}
.program-emphasis {
font-weight: 600;
color: var(--text-color);
position: relative;
transition: all var(--transition-medium);
}
.program-emphasis::after {
content: '';
position: absolute;
bottom: 0;
left: 0;
width: 100%;
height: 2px;
background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
opacity: 0;
transform: scaleX(0);
transition: all var(--transition-medium);
}
.program-section:hover .program-emphasis::after {
opacity: 0.5;
transform: scaleX(1);
}
.program-highlight {
font-weight: 700;
color: var(--text-color);
background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(220, 38, 38, 0.05));
padding: var(--space-1) var(--space-2);
border-radius: var(--radius-sm);
border: 1px solid rgba(59, 130, 246, 0.1);
transition: all var(--transition-medium);
display: inline-block;
margin: 0 2px;
}
.program-section:hover .program-highlight {
background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(220, 38, 38, 0.08));
border-color: rgba(59, 130, 246, 0.2);
transform: translateY(-1px);
box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}
.sticky-header {
position: fixed;
top: 0;
left: 0;
right: 0;
background: rgba(255, 255, 255, 0.95);
backdrop-filter: blur(20px);
-webkit-backdrop-filter: blur(20px);
z-index: 1000;
padding: var(--space-4) 0;
border-bottom: 1px solid var(--border-color);
box-shadow: var(--shadow-sm);
transform: translateY(-100%);
transition: transform var(--transition-medium);
}
.sticky-header.visible {
transform: translateY(0);
}
.sticky-header .container {
display: flex;
align-items: center;
justify-content: space-between;
}
.sticky-header .logo {
height: 56px;
}
.sticky-header .nav-links-desktop {
display: flex;
gap: var(--space-6);
list-style: none;
margin: 0;
padding: 0;
}
.sticky-header .nav-links-desktop a {
color: var(--text-color);
text-decoration: none;
font-weight: 500;
font-size: var(--text-sm);
padding: var(--space-2) var(--space-3);
border-radius: var(--radius-md);
transition: all var(--transition-fast);
}
.sticky-header .nav-links-desktop a:hover {
background: var(--bg-secondary);
color: var(--primary-color);
}
height: 100dvh;
background: var(--surface-elevated);
box-shadow: var(--shadow-xl);
transition: right var(--transition-medium);
padding: var(--space-6);
z-index: 1001;
overflow-y: auto;
}
.nav-menu.active {
right: 0;
}
.nav-overlay {
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
background: rgba(0, 0, 0, 0.4);
opacity: 0;
visibility: hidden;
transition: opacity var(--transition-medium), visibility var(--transition-medium);
z-index: 1000;
}
.nav-overlay.active {
opacity: 1;
visibility: visible;
}
.nav-links {
list-style: none;
padding: 0;
margin: var(--space-12) 0;
}
.nav-links li {
margin: var(--space-2) 0;
}
.nav-links a {
display: block;
padding: var(--space-4);
color: var(--text-color);
text-decoration: none;
border-radius: var(--radius-lg);
transition: all var(--transition-fast);
font-weight: 500;
font-size: var(--text-sm);
min-height: 44px;
}
.nav-links a:hover {
background-color: var(--bg-secondary);
color: var(--primary-color);
transform: translateX(4px);
}
min-height: 100dvh;
display: flex;
flex-direction: column;
justify-content: center;
text-align: center;
position: relative;
overflow: hidden;
padding: var(--space-20) 0;
background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%);
animation: heroGradient 8s ease-in-out infinite;
z-index: 1;
}
@keyframes heroGradient {
0%, 100% {
background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 100%);
}
50% {
background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}
}
.hero-content {
position: relative;
z-index: 2;
max-width: 900px;
margin: 0 auto;
padding: 0 var(--space-6);
}
.hero-logo {
max-width: 320px;
margin-bottom: var(--space-8);
transition: all var(--transition-medium);
filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}
.hero-logo:hover {
transform: scale(1.05);
filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
}
.hero h1 {
margin-bottom: var(--space-8);
color: var(--text-color);
line-height: var(--leading-tight);
}
.hero-tagline {
font-size: clamp(var(--text-xl), 3vw, var(--text-2xl));
font-weight: 600;
color: var(--primary-color);
margin: var(--space-8) 0 var(--space-6);
letter-spacing: -0.02em;
text-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}
.hero-subtitle {
font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
font-weight: 500;
margin: var(--space-6) 0;
color: var(--text-secondary);
}
.hero-description {
max-width: 700px;
margin: var(--space-8) auto;
font-size: var(--text-lg);
line-height: var(--leading-relaxed);
color: var(--text-secondary);
}
.hero-cta {
margin-top: var(--space-10);
}
.cta-button:visited { color: #fff; }
@media (max-width: 768px) {
.cta-button {
width: 100%;
max-width: 520px;
margin-left: auto;
margin-right: auto;
padding: var(--space-4) var(--space-6);
min-height: 52px;
}
}
.cta-button:active {
transform: translateY(0);
box-shadow: var(--shadow-md);
}
.cta-button span {
position: relative;
z-index: 1;
}
.network-bg {
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
z-index: -1;
opacity: 0.03;
}
.network-node {
position: absolute;
width: 3px;
height: 3px;
background: var(--primary-color);
border-radius: 50%;
animation: float 8s ease-in-out infinite;
box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
}
.network-line {
position: absolute;
height: 1px;
background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
opacity: 0.2;
animation: pulse 6s ease-in-out infinite;
}
@keyframes float {
0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
50% { transform: translateY(-15px) rotate(180deg); opacity: 0.6; }
}
@keyframes pulse {
0%, 100% { opacity: 0.1; transform: scaleX(0.8); }
50% { opacity: 0.3; transform: scaleX(1.2); }
}
.team-emphasis {
font-weight: 500;
color: var(--text-color);
position: relative;
transition: color var(--transition-medium);
}
.team-section:hover .team-emphasis {
color: var(--accent-green);
}
.values-section {
background: 
linear-gradient(135deg, var(--bg-color) 0%, var(--bg-secondary) 30%, var(--bg-tertiary) 70%, var(--bg-color) 100%),
radial-gradient(circle at 30% 80%, rgba(220, 38, 38, 0.015) 0%, transparent 60%),
radial-gradient(circle at 70% 20%, rgba(59, 130, 246, 0.01) 0%, transparent 60%);
padding: clamp(2.5rem, 7vw, 6rem) 0;
margin: 0;
position: relative;
overflow: hidden;
}
.values-section::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: 
url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23dc2626' fill-opacity='0.005'%3E%3Cpath d='M10 10h5v5h-5zM25 25h5v5h-5zM40 10h5v5h-5zM70 25h5v5h-5zM85 70h5v5h-5zM55 85h5v5h-5z'/%3E%3C/g%3E%3C/svg%3E");
pointer-events: none;
animation: valuesFloat 25s ease-in-out infinite;
}
@keyframes valuesFloat {
0%, 100% { transform: translateX(0px) translateY(0px) rotate(0deg); opacity: 0.2; }
25% { transform: translateX(3px) translateY(-2px) rotate(0.5deg); opacity: 0.3; }
50% { transform: translateX(-2px) translateY(-3px) rotate(-0.3deg); opacity: 0.4; }
75% { transform: translateX(-3px) translateY(1px) rotate(0.2deg); opacity: 0.3; }
}
.values-intro {
text-align: center;
max-width: 800px;
margin: 0 auto var(--space-16) auto;
position: relative;
z-index: 2;
padding: 0 var(--space-6);
}
.values-intro h2 {
margin-bottom: var(--space-8);
font-weight: 800;
font-size: clamp(var(--text-4xl), 5vw, var(--text-6xl));
background: linear-gradient(135deg, var(--text-color) 0%, var(--primary-color) 50%, var(--text-color) 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
background-clip: text;
letter-spacing: -0.02em;
line-height: var(--leading-tight);
position: relative;
}
.values-intro h2::after {
content: '';
position: absolute;
bottom: -12px;
left: 50%;
transform: translateX(-50%);
width: 120px;
height: 3px;
background: linear-gradient(90deg, transparent, var(--primary-color), var(--accent-blue), transparent);
border-radius: var(--radius-full);
opacity: 0.5;
}
.values-subtitle {
font-size: clamp(var(--text-lg), 2vw, var(--text-xl));
color: var(--text-secondary);
line-height: var(--leading-relaxed);
margin: 0;
font-weight: 400;
letter-spacing: -0.005em;
}
.values-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(min(350px, 100%), 1fr));
gap: var(--space-10);
margin-top: var(--space-16);
max-width: 1400px;
margin-left: auto;
margin-right: auto;
}
.value-emphasis {
font-weight: 500;
color: var(--text-color);
position: relative;
transition: color var(--transition-medium);
}
.value-card:hover .value-emphasis {
color: var(--primary-color);
}
.advantage-item:nth-child(1) {
background: 
linear-gradient(135deg, rgba(220, 38, 38, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.1), transparent 70%);
}
.advantage-item:nth-child(2) {
background: 
linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1), transparent 70%);
}
.advantage-item:nth-child(3) {
background: 
linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
radial-gradient(circle at 30% 30%, rgba(16, 185, 129, 0.1), transparent 70%);
}
.advantage-item:nth-child(4) {
background: 
linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 30%),
radial-gradient(circle at 30% 30%, rgba(245, 158, 11, 0.1), transparent 70%);
}
.advantage-item::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: linear-gradient(135deg, 
rgba(255, 255, 255, 0.1) 0%, 
transparent 50%, 
rgba(255, 255, 255, 0.05) 100%);
opacity: 0;
transition: opacity var(--transition-medium);
}
.advantage-item::after {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
height: 4px;
background: var(--primary-color);
opacity: 0;
transition: all var(--transition-medium);
border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}
.advantage-item:nth-child(1)::after { background: linear-gradient(90deg, var(--primary-color), var(--primary-dark)); }
.advantage-item:nth-child(2)::after { background: linear-gradient(90deg, var(--accent-blue), #1e40af); }
.advantage-item:nth-child(3)::after { background: linear-gradient(90deg, var(--accent-green), #047857); }
.advantage-item:nth-child(4)::after { background: linear-gradient(90deg, var(--accent-orange), #d97706); }
.advantage-item:hover::before {
opacity: 1;
}
.advantage-item:hover::after {
opacity: 1;
}
.advantage-item:hover {
transform: translateY(-8px) scale(1.02);
box-shadow: 
0 20px 60px rgba(0, 0, 0, 0.1),
0 8px 20px rgba(0, 0, 0, 0.06),
inset 0 1px 0 rgba(255, 255, 255, 0.8);
}
.advantage-icon {
width: 80px;
height: 80px;
margin: 0 auto var(--space-6) auto;
background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
border-radius: var(--radius-2xl);
display: flex;
align-items: center;
justify-content: center;
font-size: 2.5rem;
transition: all var(--transition-medium);
box-shadow: 
0 8px 20px rgba(220, 38, 38, 0.2),
inset 0 1px 0 rgba(255, 255, 255, 0.2);
position: relative;
z-index: 2;
}
.advantage-item:nth-child(1) .advantage-emphasis {
color: var(--text-color);
}
.advantage-item:nth-child(1):hover .advantage-emphasis::after {
background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}
.advantage-item:nth-child(2) .advantage-emphasis {
color: var(--text-color);
}
.advantage-item:nth-child(2):hover .advantage-emphasis::after {
background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
}
.advantage-item:nth-child(3) .advantage-emphasis {
color: var(--text-color);
}
.advantage-item:nth-child(3):hover .advantage-emphasis::after {
background: linear-gradient(90deg, transparent, var(--accent-green), transparent);
}
.advantage-item:nth-child(4) .advantage-emphasis {
color: var(--text-color);
}
.advantage-item:nth-child(4):hover .advantage-emphasis::after {
background: linear-gradient(90deg, transparent, var(--accent-orange), transparent);
}
.future-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
gap: var(--space-12);
margin-top: var(--space-8);
position: relative;
z-index: 2;
}
.future-card {
background: 
linear-gradient(135deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.92) 100%),
radial-gradient(circle at 30% 30%, rgba(245, 158, 11, 0.03), transparent 70%);
border: 1px solid rgba(255, 255, 255, 0.6);
padding: var(--space-12);
border-radius: var(--radius-2xl);
transition: all var(--transition-medium);
position: relative;
box-shadow: 
0 4px 20px rgba(0, 0, 0, 0.04),
0 2px 8px rgba(0, 0, 0, 0.03),
inset 0 1px 0 rgba(255, 255, 255, 0.8);
backdrop-filter: blur(20px);
-webkit-backdrop-filter: blur(20px);
overflow: hidden;
}
.future-card::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: linear-gradient(135deg, 
rgba(245, 158, 11, 0.05) 0%, 
transparent 50%, 
rgba(59, 130, 246, 0.03) 100%);
opacity: 0;
transition: opacity var(--transition-medium);
}
.future-card::after {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
height: 3px;
background: linear-gradient(90deg, transparent, var(--accent-orange), var(--accent-blue), transparent);
opacity: 0;
transition: opacity var(--transition-medium);
}
.future-card:hover::before {
opacity: 1;
}
.future-card:hover::after {
opacity: 0.6;
}
.future-card:hover {
transform: translateY(-6px) scale(1.01);
box-shadow: 
0 12px 30px rgba(0, 0, 0, 0.06),
0 6px 16px rgba(0, 0, 0, 0.04),
inset 0 1px 0 rgba(255, 255, 255, 0.9);
border-color: rgba(245, 158, 11, 0.15);
}
.future-card h3 {
font-size: var(--text-2xl);
font-weight: 700;
color: var(--text-color);
margin-bottom: var(--space-6);
letter-spacing: -0.01em;
position: relative;
z-index: 2;
}
.future-card p {
font-size: var(--text-base);
line-height: var(--leading-relaxed);
color: var(--text-secondary);
margin-bottom: var(--space-4);
position: relative;
z-index: 2;
}
.future-card p strong {
color: var(--text-color);
font-weight: 600;
}
.future-grid ul {
padding-left: 0;
list-style: none;
margin: var(--space-4) 0 0 0;
position: relative;
z-index: 2;
}
.future-grid li {
position: relative;
margin-bottom: var(--space-3);
padding-left: var(--space-8);
font-size: var(--text-base);
line-height: var(--leading-relaxed);
color: var(--text-secondary);
transition: all var(--transition-fast);
}
.future-grid li::before {
content: '✓';
position: absolute;
left: 0;
top: 0;
width: 24px;
height: 24px;
background: linear-gradient(135deg, var(--accent-orange), var(--accent-blue));
color: white;
border-radius: 50%;
display: flex;
align-items: center;
justify-content: center;
font-size: var(--text-sm);
font-weight: 600;
box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}
.future-card:hover li {
color: var(--text-color);
transform: translateX(4px);
}
.accordion-item {
background: var(--surface-elevated);
border: 1px solid var(--border-color);
border-radius: var(--radius-md);
margin-bottom: var(--space-4);
transition: all var(--transition-medium);
overflow: hidden;
box-shadow: var(--shadow-sm);
}
.accordion-item:hover {
box-shadow: var(--shadow-md);
border-color: var(--primary-light);
}
.accordion-button {
background: none;
border: none;
width: 100%;
text-align: left;
padding: var(--space-6);
font-size: var(--text-lg);
font-weight: 600;
color: var(--text-color);
cursor: pointer;
display: flex;
justify-content: space-between;
align-items: center;
transition: all var(--transition-fast);
min-height: 56px;
}
.accordion-button:hover {
color: var(--primary-color);
}
.accordion-button::after {
content: '+';
font-size: var(--text-xl);
transition: transform var(--transition-medium);
color: var(--text-tertiary);
font-weight: 300;
}
.accordion-item.active .accordion-button::after {
transform: rotate(45deg);
color: var(--primary-color);
}
.accordion-content {
max-height: 0;
overflow: hidden;
transition: max-height var(--transition-medium) ease-out;
}
.accordion-content-inner {
padding: 0 var(--space-6) var(--space-6) var(--space-6);
color: var(--text-tertiary);
line-height: var(--leading-relaxed);
display: grid;
gap: var(--space-4);
}
.final-cta {
text-align: center;
}
margin-top: clamp(2rem, 4vw, 3rem);
border-top: 1px solid var(--border-color);
position: relative;
}
footer::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: radial-gradient(circle at 20% 80%, rgba(220, 38, 38, 0.05), transparent 60%);
pointer-events: none;
}
.footer-content {
max-width: 1280px;
margin: 0 auto;
padding: 0 var(--space-6);
display: grid;
grid-template-columns: 1fr 300px; /* Fixed width for right column */
gap: var(--space-8); /* Reduced gap */
align-items: start;
position: relative;
z-index: 1;
}
.footer-main {
text-align: left;
}
.footer-logo {
max-width: 130px; /* Slightly smaller */
margin-bottom: var(--space-3); /* Reduced margin */
opacity: 0.9;
transition: all var(--transition-fast);
filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}
.footer-logo:hover {
opacity: 1;
transform: scale(1.05);
}
.footer-main h3 {
color: var(--text-color);
font-size: var(--text-xl);
margin-bottom: var(--space-3); /* Reduced margin */
font-weight: 700;
background: linear-gradient(135deg, var(--text-color), var(--primary-color));
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
background-clip: text;
}
.footer-description {
color: var(--text-secondary);
font-size: var(--text-base);
line-height: var(--leading-relaxed);
margin-bottom: var(--space-4); /* Reduced margin */
max-width: 500px;
}
.footer-contact {
display: grid;
gap: var(--space-2); /* Tighter spacing */
margin-bottom: var(--space-5); /* Reduced margin */
}
.contact-item {
display: flex;
align-items: center;
gap: var(--space-3);
padding: var(--space-2) 0; /* Reduced padding */
transition: all var(--transition-fast);
border-radius: var(--radius-md);
}
.contact-item:hover {
background: rgba(255, 255, 255, 0.5);
padding-left: var(--space-3);
}
.contact-icon {
width: 20px;
height: 20px;
color: var(--primary-color);
flex-shrink: 0;
}
.contact-item p {
margin: 0;
color: var(--text-secondary);
font-size: var(--text-sm);
font-weight: 500;
}
.contact-item a {
color: var(--text-secondary);
text-decoration: none;
transition: color var(--transition-fast);
}
.contact-item a:hover {
color: var(--primary-color);
}
.footer-social {
display: flex;
gap: var(--space-3); /* Tighter spacing */
margin-top: var(--space-3); /* Reduced margin */
}
.social-link {
display: inline-flex;
align-items: center;
justify-content: center;
width: 40px; /* Smaller icons */
height: 40px;
background: var(--surface-elevated);
border: 1px solid var(--border-color);
border-radius: var(--radius-lg);
transition: all var(--transition-medium);
text-decoration: none;
box-shadow: var(--shadow-sm);
}
.social-link:hover {
background: var(--primary-color);
border-color: var(--primary-color);
transform: translateY(-2px);
box-shadow: var(--shadow-md);
}
.social-link i {
width: 20px;
height: 20px;
color: var(--text-secondary);
transition: color var(--transition-fast);
}
.social-link:hover i {
color: white;
}
.footer-cta {
text-align: center;
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
background: rgba(255, 255, 255, 0.9);
padding: var(--space-6); /* Reduced padding */
border-radius: var(--radius-2xl);
border: 1px solid var(--border-color);
box-shadow: var(--shadow-md); /* Reduced shadow */
backdrop-filter: blur(10px);
-webkit-backdrop-filter: blur(10px);
position: relative;
overflow: hidden;
}
.footer-cta::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: linear-gradient(135deg, var(--primary-lighter), rgba(59, 130, 246, 0.05));
opacity: 0.5;
}
.footer-cta > * {
position: relative;
z-index: 1;
}
.footer-cta h4 {
color: var(--text-color);
font-size: var(--text-lg); /* Smaller font */
margin-bottom: var(--space-3); /* Reduced margin */
font-weight: 700;
}
.footer-cta p {
color: var(--text-secondary);
margin-bottom: var(--space-5); /* Reduced margin */
font-size: var(--text-sm); /* Smaller font */
line-height: var(--leading-relaxed);
}
.footer-bottom {
text-align: center;
margin-top: var(--space-8); /* Reduced margin */
padding-top: var(--space-5); /* Reduced padding */
border-top: 1px solid var(--border-color);
color: var(--text-tertiary);
font-size: var(--text-sm);
position: relative;
z-index: 1;
}
.footer-bottom p {
margin: 0;
opacity: 0.8;
}
}
.values-section,
.advantage-section,
.team-section {
padding: clamp(2rem, 6vw, 4rem) 0;
margin: 0; /* remove extra gaps between sections */
}
.values-grid,
.advantage-grid {
grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
gap: var(--space-6);
}
.team-grid {
grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
gap: var(--space-6);
}
}
@media (max-width: 768px) {
.sticky-header::after { content: none; }
.mobile-nav {
display: flex;
justify-content: space-between;
align-items: center;
}
.nav-toggle {
display: block;
}
.hero {
min-height: 100vh; /* fallback */
min-height: 100dvh;
padding: var(--space-20) 0;
}
.scholarship {
padding: var(--space-20) var(--space-6);
margin: var(--space-20) 0;
}
.scholarship-icon {
width: 64px;
height: 64px;
margin-bottom: var(--space-4);
}
.scholarship h2 {
font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
margin-bottom: var(--space-6);
}
.scholarship-subtitle {
font-size: var(--text-lg);
margin-bottom: var(--space-6);
}
.scholarship-description {
font-size: var(--text-base);
margin-bottom: var(--space-8);
}
.scholarship-features {
grid-template-columns: 1fr;
gap: var(--space-6);
margin: var(--space-10) 0;
}
.scholarship-feature {
padding: var(--space-6);
}
.feature-icon {
width: 40px;
height: 40px;
margin-bottom: var(--space-3);
}
.scholarship-highlight {
padding: var(--space-5) var(--space-6);
margin: var(--space-8) auto;
}
.scholarship-highlight .highlight-title {
font-size: var(--text-xl);
margin-bottom: var(--space-2);
}
.scholarship-highlight .highlight-text {
font-size: var(--text-base);
}
.scholarship-cta .cta-button {
padding: var(--space-4) var(--space-8);
font-size: var(--text-base);
}
.cta-button {
padding: var(--space-4) var(--space-8);
font-size: var(--text-base);
}
.future-grid {
grid-template-columns: 1fr;
gap: var(--space-8);
}
.future-card {
padding: var(--space-6);
}
.values-section,
.advantage-section,
.team-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }
.values-section { padding: clamp(2rem, 6vw, 4rem) 0; margin: 0; }
.values-intro {
margin-bottom: var(--space-12);
padding: 0 var(--space-4);
}
.values-intro h2 {
font-size: clamp(var(--text-3xl), 7vw, var(--text-5xl));
margin-bottom: var(--space-6);
}
.values-intro h2::after {
width: 80px;
height: 2px;
bottom: -10px;
}
.values-subtitle {
font-size: clamp(var(--text-base), 3vw, var(--text-lg));
}
.values-grid,
.advantage-grid {
grid-template-columns: 1fr;
gap: var(--space-8);
}
.value-card {
padding: var(--space-10);
}
.value-icon {
width: 64px;
height: 64px;
margin-bottom: var(--space-5);
}
.value-icon i {
width: 28px;
height: 28px;
}
.value-card h3 {
font-size: var(--text-lg);
margin-bottom: var(--space-4);
}
.value-emphasis {
font-weight: 500;
}
.advantage-item {
padding: var(--space-8);
}
.advantage-icon {
width: 64px;
height: 64px;
font-size: 2rem;
}
.advantage-item h3 {
font-size: var(--text-lg);
}
.advantage-emphasis {
font-weight: 600;
}
.team-grid {
grid-template-columns: 1fr;
gap: var(--space-6);
overflow-x: auto;
scroll-snap-type: x mandatory;
display: flex;
padding: 0 var(--space-4) var(--space-4) 0;
}
.team-card {
min-width: 280px;
flex-shrink: 0;
padding: var(--space-8);
}
.team-avatar {
width: 64px;
height: 64px;
font-size: var(--text-xl);
}
.team-card h3 {
font-size: var(--text-lg);
}
.team-link {
padding: var(--space-2) var(--space-4);
font-size: var(--text-xs);
}
section {
padding: clamp(1.75rem, 6vw, 3.5rem) 0; /* compact, readable spacing on mobile */
}
.faq-header { margin-bottom: var(--space-8); }
.accordion-button {
padding: var(--space-4) var(--space-5);
font-size: var(--text-base);
min-height: 48px; /* touch target */
}
.accordion-content-inner {
padding: 0 var(--space-3) var(--space-5) var(--space-3);
}
.faq-details { padding: var(--space-3); border-left-width: 2px; }
.container {
padding: 0 var(--space-4);
}
}
@media (max-width: 480px) {
section { padding: clamp(1.5rem, 6vw, 3rem) 0; }
h1 {
font-size: var(--text-4xl);
}
h2 {
font-size: var(--text-3xl);
}
.hero h1 {
font-size: var(--text-4xl);
}
.hero-tagline {
font-size: var(--text-xl);
}
.hero-subtitle {
font-size: var(--text-lg);
}
.hero-description {
font-size: var(--text-base);
}
.cta-button {
padding: var(--space-4) var(--space-7);
font-size: var(--text-base);
min-height: 48px; /* touch target */
}
.hero-logo {
max-width: 180px;
}
.program-section { padding: clamp(1.75rem, 6vw, 3.5rem) 0; margin: 0; }
.program-section h2 {
font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
margin-bottom: var(--space-6);
}
.program-section h2::after {
width: 80px;
height: 2px;
bottom: -12px;
}
.program-description {
font-size: clamp(var(--text-sm), 4vw, var(--text-base));
}
.program-description::before,
.program-description::after {
font-size: 1.5rem;
}
.program-description::before {
top: -15px;
left: -25px;
}
.program-description::after {
bottom: -15px;
right: -25px;
}
.program-highlight {
padding: 1px 4px;
font-size: var(--text-xs);
}
.team-section h2 {
font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
margin-bottom: var(--space-5);
}
.team-section h2::after {
width: 60px;
height: 2px;
bottom: -10px;
}
.team-intro {
font-size: clamp(var(--text-sm), 4vw, var(--text-base));
}
.team-intro::before,
.team-intro::after {
font-size: 1.5rem;
}
.team-intro::before {
top: -15px;
left: -25px;
}
.team-intro::after {
bottom: -15px;
right: -25px;
}
.future-section h2 {
font-size: clamp(var(--text-2xl), 6vw, var(--text-4xl));
margin-bottom: var(--space-5);
}
.future-section h2::after {
width: 80px;
height: 2px;
bottom: -10px;
}
.future-intro {
font-size: clamp(var(--text-sm), 4vw, var(--text-base));
}
.future-intro::before,
.future-intro::after {
font-size: 1.5rem;
}
.future-intro::before {
top: -15px;
left: -25px;
}
.future-intro::after {
bottom: -15px;
right: -25px;
}
.future-card {
padding: var(--space-8);
}
.future-card h3 {
font-size: var(--text-xl);
margin-bottom: var(--space-4);
}
.future-grid li {
padding-left: var(--space-6);
margin-bottom: var(--space-2);
}
.future-grid li::before {
width: 20px;
height: 20px;
font-size: var(--text-xs);
}
.value-card {
padding: var(--space-8);
}
.value-icon {
width: 56px;
height: 56px;
margin-bottom: var(--space-4);
}
.value-icon i {
width: 24px;
height: 24px;
}
.value-card h3 {
font-size: var(--text-base);
margin-bottom: var(--space-3);
}
.advantage-item {
padding: var(--space-6);
}
.advantage-icon {
width: 56px;
height: 56px;
font-size: 1.5rem;
}
.advantage-item h3 {
font-size: var(--text-base);
margin-bottom: var(--space-3);
}
.advantage-emphasis {
font-weight: 600;
}
.team-card {
min-width: 260px;
padding: var(--space-6);
}
.team-avatar {
width: 56px;
height: 56px;
font-size: var(--text-lg);
}
.team-card h3 {
font-size: var(--text-base);
}
.team-link {
padding: var(--space-2) var(--space-3);
font-size: var(--text-xs);
}
.accordion-button {
padding: var(--space-4);
font-size: var(--text-base);
}
.accordion-content-inner {
padding: 0 var(--space-4) var(--space-4) var(--space-4);
}
}
section {
position: relative;
}
section::before {
content: '';
position: absolute;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: 
url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
repeating-linear-gradient(
0deg,
transparent,
transparent 2px,
rgba(0, 0, 0, 0.01) 2px,
rgba(0, 0, 0, 0.01) 4px
);
z-index: 0;
opacity: 0.4;
pointer-events: none;
mix-blend-mode: multiply;
}
.vision-section::before {
opacity: 0.4;
background: 
url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
radial-gradient(circle at 30% 30%, rgba(220, 38, 38, 0.04) 0%, transparent 60%),
repeating-linear-gradient(
45deg,
transparent,
transparent 2px,
rgba(0, 0, 0, 0.01) 2px,
rgba(0, 0, 0, 0.01) 4px
);
}
#team::before {
opacity: 0.35;
background: 
url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
radial-gradient(circle at 25% 75%, rgba(16, 185, 129, 0.02) 0%, transparent 60%),
repeating-linear-gradient(
90deg,
transparent,
transparent 2px,
rgba(0, 0, 0, 0.01) 2px,
rgba(0, 0, 0, 0.01) 4px
);
}
#advantage::before {
opacity: 0.35;
background: 
url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
radial-gradient(circle at 10% 20%, rgba(220, 38, 38, 0.02) 0%, transparent 50%),
repeating-linear-gradient(
180deg,
transparent,
transparent 2px,
rgba(0, 0, 0, 0.01) 2px,
rgba(0, 0, 0, 0.01) 4px
);
}
#logistics::before {
opacity: 0.35;
background: 
url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='53' cy='7' r='1'/%3E%3Ccircle cx='7' cy='53' r='1'/%3E%3Ccircle cx='53' cy='53' r='1'/%3E%3C/g%3E%3C/svg%3E"),
radial-gradient(circle at 85% 15%, rgba(59, 130, 246, 0.02) 0%, transparent 60%),
repeating-linear-gradient(
270deg,
transparent,
transparent 2px,
rgba(0, 0, 0, 0.01) 2px,
rgba(0, 0, 0, 0.01) 4px
);
}
.hero-content,
.vision-content, 
.program-content,
.team-intro-container,
.advantage-intro,
.future-intro-container,
.values-intro,
.container {
position: relative;
z-index: 2;
}
.hero {
padding: clamp(5rem, 12vh, 10rem) 0;
background: transparent; /* inherit page background texture */
animation: none;
}
.hero::before { display: none; }
.parallax-bg { opacity: 0.18; }
.network-bg { opacity: 0.02; }
.hero-logo {
max-width: 180px;
margin-bottom: var(--space-6);
filter: none;
}
.hero h1 {
font-size: clamp(2rem, 5vw, 3.25rem);
letter-spacing: -0.02em;
margin-bottom: var(--space-4);
}
.hero-tagline {
color: var(--text-secondary);
margin: var(--space-3) 0;
text-shadow: none;
}
.hero-subtitle {
color: var(--text-tertiary);
margin-top: var(--space-2);
}
.hero-description {
max-width: 46ch;
margin-top: var(--space-6);
color: var(--text-secondary);
}
.hero-cta { margin-top: var(--space-8); }
.faq-header {
text-align: center;
margin-bottom: var(--space-12);
}
.faq-subtitle {
color: var(--text-secondary);
font-size: var(--text-lg);
margin-top: var(--space-4);
max-width: 600px;
margin-left: auto;
margin-right: auto;
}
.faq-highlight {
color: var(--primary-color);
font-weight: 700;
}
.faq-emphasis {
color: var(--text-color);
font-weight: 600;
background: linear-gradient(135deg, var(--primary-lighter), transparent);
padding: 2px 6px;
border-radius: 4px;
}
.faq-details {
margin-top: var(--space-4);
padding: var(--space-4);
background: var(--bg-secondary);
border-radius: var(--radius-lg);
border-left: 3px solid var(--primary-color);
}
.faq-details h4 {
color: var(--text-color);
font-size: var(--text-base);
font-weight: 600;
margin-bottom: var(--space-3);
}
.faq-details ul {
margin: var(--space-3) 0;
padding-left: var(--space-5);
}
.faq-details li {
margin-bottom: var(--space-2);
color: var(--text-secondary);
}
.exam-breakdown {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
gap: var(--space-4);
margin: var(--space-4) 0;
}
.exam-subject {
padding: var(--space-3);
background: var(--bg-color);
border-radius: var(--radius-md);
border: 1px solid var(--border-color);
}
.exam-subject h4 {
color: var(--primary-color);
font-size: var(--text-sm);
font-weight: 700;
margin-bottom: var(--space-1);
}
.exam-subject p {
color: var(--text-tertiary);
font-size: var(--text-sm);
margin: 0;
}
.contact-promise {
background: var(--primary-lighter);
padding: var(--space-3);
border-radius: var(--radius-md);
margin: var(--space-3) 0;
border: 1px solid var(--primary-light);
}
.contact-promise p {
margin: 0;
color: var(--primary-dark);
}
.contact-info ul {
display: flex;
gap: var(--space-4);
flex-wrap: wrap;
margin: var(--space-3) 0 0 0;
padding: 0;
list-style: none;
}
.contact-info li {
background: var(--bg-color);
padding: var(--space-2) var(--space-3);
border-radius: var(--radius-md);
border: 1px solid var(--border-color);
font-size: var(--text-sm);
}
.program-structure,
.differentiators,
.time-commitment {
margin: var(--space-3) 0;
}
.timeline-grid {
display: grid;
grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
gap: var(--space-4);
margin: var(--space-4) 0;
}
.timeline-item {
padding: var(--space-4);
background: var(--bg-color);
border-radius: var(--radius-md);
border: 1px solid var(--border-color);
text-align: center;
position: relative;
}
.timeline-date {
font-size: var(--text-lg);
font-weight: 700;
color: var(--primary-color);
margin-bottom: var(--space-2);
}
.timeline-event {
color: var(--text-secondary);
font-weight: 500;
}
.test-center-info {
background: var(--primary-lighter);
padding: var(--space-4);
border-radius: var(--radius-md);
margin: var(--space-4) 0;
border: 1px solid var(--primary-light);
}
.test-center-info h4 {
color: var(--primary-dark);
margin-bottom: var(--space-2);
font-size: var(--text-base);
}
.test-center-info p {
color: var(--primary-darker);
margin: 0;
line-height: 1.6;
}
@media (max-width: 768px) {
.exam-breakdown {
grid-template-columns: 1fr;
gap: var(--space-3);
}
.timeline-grid {
grid-template-columns: 1fr;
gap: var(--space-3);
}
.contact-info ul {
flex-direction: column;
gap: var(--space-2);
}
}
